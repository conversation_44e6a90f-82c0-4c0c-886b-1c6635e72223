<template>
  <view class="face-verify-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="人脸识别验证" 
      :autoBack="false"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 验证步骤指示 -->
    <view class="verify-steps">
      <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <view class="step-icon">
          <u-icon v-if="currentStep > 1" name="checkmark" color="#fff" size="24" />
          <text v-else>1</text>
        </view>
        <text class="step-text">身份确认</text>
      </view>
      <view class="step-line" :class="{ active: currentStep > 1 }"></view>
      <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <view class="step-icon">
          <u-icon v-if="currentStep > 2" name="checkmark" color="#fff" size="24" />
          <text v-else>2</text>
        </view>
        <text class="step-text">人脸识别</text>
      </view>
      <view class="step-line" :class="{ active: currentStep > 2 }"></view>
      <view class="step-item" :class="{ active: currentStep >= 3 }">
        <view class="step-icon">
          <text>3</text>
        </view>
        <text class="step-text">验证完成</text>
      </view>
    </view>
    
    <!-- 身份确认步骤 -->
    <view v-if="currentStep === 1" class="step-content">
      <view class="identity-confirm">
        <view class="user-info-card">
          <view class="avatar-section">
            <image 
              :src="userInfo?.photo || '/static/images/default-avatar.png'" 
              class="user-avatar"
              mode="aspectFill"
            />
          </view>
          <view class="info-section">
            <text class="user-name">{{ userInfo?.realName }}</text>
            <text class="user-id">身份证：{{ maskIdCard(userInfo?.idCard || '') }}</text>
            <text class="user-org">{{ userInfo?.organization }}</text>
          </view>
        </view>
        
        <view class="confirm-tips">
          <u-icon name="info-circle" color="#4A90E2" size="32" />
          <text class="tips-text">请确认以上信息是否为您本人信息，确认无误后进行人脸识别验证</text>
        </view>
        
        <u-button 
          class="confirm-btn"
          type="primary"
          @click="confirmIdentity"
        >
          确认身份，开始验证
        </u-button>
      </view>
    </view>
    
    <!-- 人脸识别步骤 -->
    <view v-else-if="currentStep === 2" class="step-content">
      <view class="face-recognition">
        <view class="camera-section">
          <view class="camera-frame">
            <camera 
              v-if="showCamera"
              class="camera-view"
              device-position="front"
              flash="off"
              @error="onCameraError"
            />
            <view v-else class="camera-placeholder">
              <u-icon name="camera" color="#ccc" size="120" />
              <text class="placeholder-text">摄像头加载中...</text>
            </view>
            
            <!-- 人脸框 -->
            <view class="face-frame">
              <view class="frame-corner tl"></view>
              <view class="frame-corner tr"></view>
              <view class="frame-corner bl"></view>
              <view class="frame-corner br"></view>
            </view>
            
            <!-- 验证状态提示 -->
            <view v-if="verifyStatus" class="verify-status" :class="verifyStatus">
              <text>{{ getStatusText() }}</text>
            </view>
          </view>
          
          <view class="camera-tips">
            <text class="tips-title">请按照以下要求进行人脸识别：</text>
            <view class="tips-list">
              <view class="tip-item">
                <u-icon name="checkmark-circle" color="#4CAF50" size="24" />
                <text>保持面部正对摄像头</text>
              </view>
              <view class="tip-item">
                <u-icon name="checkmark-circle" color="#4CAF50" size="24" />
                <text>确保光线充足，面部清晰</text>
              </view>
              <view class="tip-item">
                <u-icon name="checkmark-circle" color="#4CAF50" size="24" />
                <text>摘下眼镜、帽子等遮挡物</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="verify-actions">
          <u-button 
            class="capture-btn"
            type="primary"
            :loading="isVerifying"
            :disabled="!showCamera"
            loadingText="识别中..."
            @click="captureAndVerify"
          >
            {{ retryCount > 0 ? `重新识别 (${maxRetry - retryCount}/${maxRetry})` : '开始识别' }}
          </u-button>
          
          <view v-if="retryCount > 0" class="retry-tips">
            <text>识别失败，请调整姿势和光线后重试</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 验证完成步骤 -->
    <view v-else-if="currentStep === 3" class="step-content">
      <view class="verify-success">
        <view class="success-icon">
          <u-icon name="checkmark-circle-fill" color="#4CAF50" size="120" />
        </view>
        <text class="success-title">人脸识别验证成功</text>
        <text class="success-desc">身份验证通过，即将进入考试界面</text>
        
        <view class="verify-result">
          <view class="result-item">
            <text class="result-label">相似度：</text>
            <text class="result-value">{{ similarity }}%</text>
          </view>
          <view class="result-item">
            <text class="result-label">验证时间：</text>
            <text class="result-value">{{ formatDate(new Date(), 'HH:mm:ss') }}</text>
          </view>
        </view>
        
        <u-button 
          class="enter-exam-btn"
          type="primary"
          :loading="isEntering"
          loadingText="进入中..."
          @click="enterExam"
        >
          进入考试
        </u-button>
      </view>
    </view>
    
    <!-- 验证失败弹窗 -->
    <u-modal 
      v-model="showFailModal"
      title="验证失败"
      :content="failMessage"
      :showCancelButton="retryCount < maxRetry"
      :confirmText="retryCount >= maxRetry ? '返回' : '重试'"
      :cancelText="'退出考试'"
      @confirm="handleFailConfirm"
      @cancel="exitExam"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { formatDate, maskIdCard } from '@/utils'
import { PAGE_PATHS, EXAM_CONFIG } from '@/constants'
import api from '@/api'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  examId: string
}>()

// 响应式数据
const currentStep = ref(1)
const showCamera = ref(false)
const isVerifying = ref(false)
const isEntering = ref(false)
const verifyStatus = ref('')
const retryCount = ref(0)
const similarity = ref(0)
const showFailModal = ref(false)
const failMessage = ref('')

// 配置
const maxRetry = EXAM_CONFIG.FACE_VERIFY_MAX_RETRY

// 计算属性
const userInfo = computed(() => userStore.userInfo)

onMounted(() => {
  // 初始化摄像头
  initCamera()
})

onUnmounted(() => {
  // 清理资源
  if (showCamera.value) {
    showCamera.value = false
  }
})

// 初始化摄像头
const initCamera = async () => {
  try {
    // 检查摄像头权限
    await uni.authorize({
      scope: 'scope.camera'
    })
    
    // 延迟显示摄像头，确保页面渲染完成
    setTimeout(() => {
      showCamera.value = true
    }, 500)
  } catch (error) {
    console.error('摄像头初始化失败:', error)
    appStore.showModal({
      title: '摄像头权限',
      content: '需要摄像头权限进行人脸识别验证，请在设置中开启权限。',
      confirmText: '去设置',
      cancelText: '退出'
    }).then((confirmed) => {
      if (confirmed) {
        uni.openSetting()
      } else {
        exitExam()
      }
    })
  }
}

// 摄像头错误处理
const onCameraError = (error: any) => {
  console.error('摄像头错误:', error)
  appStore.showToast('摄像头启动失败，请重试')
  showCamera.value = false
  
  // 重新初始化
  setTimeout(() => {
    initCamera()
  }, 2000)
}

// 确认身份
const confirmIdentity = () => {
  currentStep.value = 2
}

// 获取状态文本
const getStatusText = () => {
  const statusMap: Record<string, string> = {
    detecting: '正在检测人脸...',
    verifying: '正在验证身份...',
    success: '验证成功',
    failed: '验证失败'
  }
  return statusMap[verifyStatus.value] || ''
}

// 拍照并验证
const captureAndVerify = async () => {
  if (isVerifying.value) return
  
  isVerifying.value = true
  verifyStatus.value = 'detecting'
  
  try {
    // 创建camera上下文
    const cameraContext = uni.createCameraContext()
    
    // 拍照
    const photo = await new Promise<string>((resolve, reject) => {
      cameraContext.takePhoto({
        quality: 'high',
        success: (res) => {
          resolve(res.tempImagePath)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
    
    verifyStatus.value = 'verifying'
    
    // 调用人脸识别API
    const response = await api.exam.verifyFace({
      examId: props.examId,
      photo: photo
    })
    
    if (response.data.success) {
      // 验证成功
      similarity.value = Math.round(response.data.similarity * 100)
      verifyStatus.value = 'success'
      
      setTimeout(() => {
        currentStep.value = 3
      }, 1500)
    } else {
      // 验证失败
      handleVerifyFailed('人脸识别失败，请确保面部清晰可见')
    }
  } catch (error: any) {
    console.error('人脸识别失败:', error)
    handleVerifyFailed(error.message || '识别过程中出现错误，请重试')
  } finally {
    isVerifying.value = false
  }
}

// 处理验证失败
const handleVerifyFailed = (message: string) => {
  verifyStatus.value = 'failed'
  retryCount.value++
  
  if (retryCount.value >= maxRetry) {
    failMessage.value = `验证失败次数过多，无法继续考试。${message}`
  } else {
    failMessage.value = `${message}，还可重试 ${maxRetry - retryCount.value} 次。`
  }
  
  showFailModal.value = true
}

// 处理失败确认
const handleFailConfirm = () => {
  showFailModal.value = false
  
  if (retryCount.value >= maxRetry) {
    // 超过最大重试次数，退出考试
    exitExam()
  } else {
    // 重置状态，准备重试
    verifyStatus.value = ''
  }
}

// 进入考试
const enterExam = async () => {
  isEntering.value = true
  
  try {
    // 开始考试
    const response = await api.exam.startExam(props.examId)
    
    // 跳转到答题页面
    appStore.redirectTo(PAGE_PATHS.EXAM_ONLINE_ANSWER, { 
      examId: props.examId,
      recordId: response.data.recordId
    })
  } catch (error: any) {
    console.error('开始考试失败:', error)
    appStore.showToast(error.message || '开始考试失败，请重试')
  } finally {
    isEntering.value = false
  }
}

// 退出考试
const exitExam = () => {
  appStore.showModal({
    title: '确认退出',
    content: '确定要退出考试吗？退出后需要重新进行身份验证。',
    confirmText: '确认退出',
    cancelText: '取消'
  }).then((confirmed) => {
    if (confirmed) {
      appStore.navigateBack(2) // 返回到考试中心
    }
  })
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.face-verify-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.verify-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 60rpx;
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  
  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .step-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 30rpx;
      background: #e9ecef;
      color: #6c757d;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: bold;
      margin-bottom: 12rpx;
      transition: all 0.3s ease;
    }
    
    .step-text {
      font-size: 24rpx;
      color: #6c757d;
      transition: all 0.3s ease;
    }
    
    &.active {
      .step-icon {
        background: #4A90E2;
        color: #fff;
      }
      
      .step-text {
        color: #4A90E2;
        font-weight: bold;
      }
    }
    
    &.completed {
      .step-icon {
        background: #4CAF50;
        color: #fff;
      }
      
      .step-text {
        color: #4CAF50;
        font-weight: bold;
      }
    }
  }
  
  .step-line {
    width: 80rpx;
    height: 4rpx;
    background: #e9ecef;
    margin: 0 20rpx;
    margin-bottom: 30rpx;
    transition: all 0.3s ease;
    
    &.active {
      background: #4CAF50;
    }
  }
}

.step-content {
  margin: 24rpx;
}

.identity-confirm {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .user-info-card {
    display: flex;
    align-items: center;
    padding: 32rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    margin-bottom: 40rpx;
    
    .avatar-section {
      margin-right: 32rpx;
      
      .user-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        border: 4rpx solid #4A90E2;
      }
    }
    
    .info-section {
      flex: 1;
      
      .user-name {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .user-id {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .user-org {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .confirm-tips {
    display: flex;
    align-items: flex-start;
    padding: 24rpx;
    background: #e6f7ff;
    border-radius: 12rpx;
    border-left: 6rpx solid #4A90E2;
    margin-bottom: 40rpx;
    
    .tips-text {
      flex: 1;
      margin-left: 16rpx;
      font-size: 26rpx;
      line-height: 1.5;
      color: #333;
    }
  }
  
  .confirm-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}

.face-recognition {
  .camera-section {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    
    .camera-frame {
      position: relative;
      width: 100%;
      height: 500rpx;
      border-radius: 16rpx;
      overflow: hidden;
      margin-bottom: 40rpx;
      
      .camera-view {
        width: 100%;
        height: 100%;
      }
      
      .camera-placeholder {
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        
        .placeholder-text {
          margin-top: 20rpx;
          font-size: 26rpx;
          color: #999;
        }
      }
      
      .face-frame {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 300rpx;
        height: 400rpx;
        
        .frame-corner {
          position: absolute;
          width: 40rpx;
          height: 40rpx;
          border: 4rpx solid #4A90E2;
          
          &.tl {
            top: 0;
            left: 0;
            border-right: none;
            border-bottom: none;
          }
          
          &.tr {
            top: 0;
            right: 0;
            border-left: none;
            border-bottom: none;
          }
          
          &.bl {
            bottom: 0;
            left: 0;
            border-right: none;
            border-top: none;
          }
          
          &.br {
            bottom: 0;
            right: 0;
            border-left: none;
            border-top: none;
          }
        }
      }
      
      .verify-status {
        position: absolute;
        bottom: 20rpx;
        left: 50%;
        transform: translateX(-50%);
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: bold;
        
        &.detecting {
          background: rgba(255, 149, 0, 0.9);
          color: #fff;
        }
        
        &.verifying {
          background: rgba(74, 144, 226, 0.9);
          color: #fff;
        }
        
        &.success {
          background: rgba(76, 175, 80, 0.9);
          color: #fff;
        }
        
        &.failed {
          background: rgba(245, 108, 108, 0.9);
          color: #fff;
        }
      }
    }
    
    .camera-tips {
      .tips-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .tips-list {
        .tip-item {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;
          
          text {
            margin-left: 12rpx;
            font-size: 26rpx;
            color: #666;
          }
        }
      }
    }
  }
  
  .verify-actions {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    
    .capture-btn {
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .retry-tips {
      text-align: center;
      
      text {
        font-size: 24rpx;
        color: #f56c6c;
      }
    }
  }
}

.verify-success {
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .success-icon {
    margin-bottom: 40rpx;
  }
  
  .success-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .success-desc {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 40rpx;
  }
  
  .verify-result {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 32rpx;
    margin-bottom: 40rpx;
    
    .result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .result-label {
        font-size: 26rpx;
        color: #666;
      }
      
      .result-value {
        font-size: 26rpx;
        font-weight: bold;
        color: #4A90E2;
      }
    }
  }
  
  .enter-exam-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
