<template>
  <view class="exam-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">考试中心</text>
        <view class="header-actions">
          <u-icon name="calendar" color="#fff" size="44" @click="viewCalendar" />
        </view>
      </view>
    </view>
    
    <!-- 权限控制包装器 -->
    <PermissionWrapper permission="authenticated" :showFallback="true">
      <!-- 非认证用户提示 -->
      <template #fallback>
        <view class="auth-prompt">
          <view class="prompt-card">
            <u-icon name="lock" color="#f56c6c" size="80" />
            <text class="prompt-title">未认证，无法考试</text>
            <text class="prompt-desc">请先完善个人资料并通过机构审核，才能参加考试</text>
            <u-button 
              class="auth-btn" 
              type="primary" 
              size="medium"
              @click="goToAuth"
            >
              去完善资料
            </u-button>
          </view>
        </view>
      </template>
      
      <!-- 认证用户考试内容 -->
      <view class="main-content">
        <!-- 考试统计卡片 -->
        <view class="stats-card">
          <view class="stats-item">
            <text class="stats-number">{{ examStats.total }}</text>
            <text class="stats-label">参加考试</text>
          </view>
          <view class="stats-divider"></view>
          <view class="stats-item">
            <text class="stats-number">{{ examStats.passed }}</text>
            <text class="stats-label">通过考试</text>
          </view>
          <view class="stats-divider"></view>
          <view class="stats-item">
            <text class="stats-number">{{ examStats.pending }}</text>
            <text class="stats-label">待参加</text>
          </view>
        </view>
        
        <!-- 本期考试 -->
        <view class="current-exams-section">
          <view class="section-header">
            <text class="section-title">本期考试</text>
            <text class="section-desc">请及时参加考试，不要错过考试时间</text>
          </view>
          
          <!-- 加载状态 -->
          <LoadingSpinner v-if="isLoading" text="加载考试信息..." />
          
          <!-- 空状态 -->
          <EmptyState 
            v-else-if="currentExams.length === 0"
            type="no-data"
            title="暂无待参加的考试"
            description="当前没有可参加的考试，请关注最新考试通知"
            :showButton="false"
          />
          
          <!-- 考试列表 -->
          <view v-else class="exam-list">
            <view 
              v-for="exam in currentExams" 
              :key="exam.id"
              class="exam-card"
              @click="handleExamClick(exam)"
            >
              <!-- 考试类型标签 -->
              <view class="exam-type-tag" :class="exam.type">
                <text>{{ exam.type === 'online' ? '线上考试' : '线下考试' }}</text>
              </view>
              
              <!-- 考试信息 -->
              <view class="exam-info">
                <text class="exam-title">{{ exam.name }}</text>
                <text class="exam-desc">{{ exam.description }}</text>
                
                <view class="exam-meta">
                  <view class="meta-item">
                    <u-icon name="clock" color="#666" size="24" />
                    <text>{{ formatExamTime(exam) }}</text>
                  </view>
                  <view class="meta-item">
                    <u-icon name="time" color="#666" size="24" />
                    <text>{{ exam.duration }}分钟</text>
                  </view>
                  <view v-if="exam.type === 'offline'" class="meta-item">
                    <u-icon name="location" color="#666" size="24" />
                    <text>{{ getVenueText(exam) }}</text>
                  </view>
                </view>
              </view>
              
              <!-- 考试状态和操作 -->
              <view class="exam-actions">
                <StatusTag :type="'exam'" :status="exam.status" />
                
                <u-button 
                  class="action-btn"
                  :type="getButtonType(exam.status)"
                  size="small"
                  :disabled="!canAction(exam)"
                  @click.stop="handleAction(exam)"
                >
                  {{ getActionText(exam) }}
                </u-button>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 历史考试记录入口 -->
        <view class="history-section">
          <view class="history-card" @click="viewHistory">
            <view class="history-info">
              <u-icon name="file-text" color="#4A90E2" size="60" />
              <view class="history-content">
                <text class="history-title">历史考试记录</text>
                <text class="history-desc">查看所有考试记录和成绩</text>
              </view>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
        </view>
      </view>
    </PermissionWrapper>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onShow } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { formatDate } from '@/utils'
import { PAGE_PATHS } from '@/constants'
import api from '@/api'
import type { Exam } from '@/types'

// 导入组件
import PermissionWrapper from '@/src/components/common/PermissionWrapper.vue'
import LoadingSpinner from '@/src/components/common/LoadingSpinner.vue'
import EmptyState from '@/src/components/common/EmptyState.vue'
import StatusTag from '@/src/components/common/StatusTag.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 系统信息
const statusBarHeight = ref(0)

// 数据状态
const isLoading = ref(true)
const currentExams = ref<Exam[]>([])

// 考试统计数据
const examStats = ref({
  total: 0,
  passed: 0,
  pending: 0
})

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 初始化数据
  if (userStore.isAuthenticated) {
    loadExamData()
  }
})

onShow(() => {
  // 页面显示时刷新数据
  if (userStore.isAuthenticated) {
    loadExamData()
  }
})

// 加载考试数据
const loadExamData = async () => {
  isLoading.value = true
  
  try {
    // 加载当前考试列表
    const response = await api.exam.getCurrentExams()
    currentExams.value = response.data
    
    // 加载考试统计（这里可以从考试记录API获取）
    // const statsResponse = await api.exam.getExamStats()
    // examStats.value = statsResponse.data
    
    // 临时模拟数据
    examStats.value = {
      total: 12,
      passed: 10,
      pending: currentExams.value.length
    }
  } catch (error: any) {
    console.error('加载考试数据失败:', error)
    appStore.showToast(error.message || '加载失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 去认证
const goToAuth = () => {
  if (userStore.userInfo?.status === 'not_submitted') {
    appStore.redirectTo(PAGE_PATHS.PROFILE)
  } else {
    appStore.switchTab(PAGE_PATHS.PERSONAL)
  }
}

// 查看日历
const viewCalendar = () => {
  appStore.showToast('考试日历功能开发中')
}

// 格式化考试时间
const formatExamTime = (exam: Exam) => {
  const start = new Date(exam.startTime)
  const end = new Date(exam.endTime)
  
  if (exam.type === 'online') {
    return `${formatDate(start, 'MM月DD日 HH:mm')}-${formatDate(end, 'HH:mm')}`
  } else {
    return `${formatDate(start, 'MM月DD日 HH:mm')}`
  }
}

// 获取场地文本
const getVenueText = (exam: Exam) => {
  // 这里应该从场地信息中获取
  return '待安排'
}

// 获取按钮类型
const getButtonType = (status: string) => {
  const typeMap: Record<string, string> = {
    'not_started': 'primary',
    'in_progress': 'warning',
    'completed': 'info',
    'expired': 'info'
  }
  return typeMap[status] || 'default'
}

// 是否可以操作
const canAction = (exam: Exam) => {
  return !['completed', 'expired'].includes(exam.status)
}

// 获取操作文本
const getActionText = (exam: Exam) => {
  const actionMap: Record<string, string> = {
    'not_started': '准备考试',
    'in_progress': '继续考试',
    'completed': '查看成绩',
    'expired': '已过期'
  }
  return actionMap[exam.status] || '查看详情'
}

// 处理考试点击
const handleExamClick = (exam: Exam) => {
  // 点击卡片区域的处理逻辑
  if (exam.type === 'online') {
    handleOnlineExam(exam)
  } else {
    handleOfflineExam(exam)
  }
}

// 处理操作按钮点击
const handleAction = (exam: Exam) => {
  if (exam.type === 'online') {
    handleOnlineExam(exam)
  } else {
    handleOfflineExam(exam)
  }
}

// 处理线上考试
const handleOnlineExam = (exam: Exam) => {
  if (exam.status === 'not_started') {
    // 跳转到考前阅读
    appStore.navigateTo(PAGE_PATHS.EXAM_ONLINE_READING, { examId: exam.id })
  } else if (exam.status === 'in_progress') {
    // 继续考试
    appStore.showModal({
      title: '继续考试',
      content: '检测到您有未完成的考试，是否继续？',
      confirmText: '继续考试',
      cancelText: '取消'
    }).then((confirmed) => {
      if (confirmed) {
        appStore.navigateTo(PAGE_PATHS.EXAM_ONLINE_ANSWER, { examId: exam.id })
      }
    })
  } else if (exam.status === 'completed') {
    // 查看成绩
    appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY, { examId: exam.id })
  }
}

// 处理线下考试
const handleOfflineExam = (exam: Exam) => {
  // 跳转到线下考试详情页面
  appStore.navigateTo(PAGE_PATHS.EXAM_OFFLINE_DETAIL, { examId: exam.id })
}

// 查看历史记录
const viewHistory = () => {
  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY)
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.exam-center-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
  }
}

.auth-prompt {
  padding: 60rpx 30rpx;
  
  .prompt-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 80rpx 40rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    
    .prompt-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin: 40rpx 0 20rpx;
    }
    
    .prompt-desc {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 60rpx;
    }
    
    .auth-btn {
      width: 300rpx;
      height: 80rpx;
      border-radius: 40rpx;
    }
  }
}

.main-content {
  padding: 30rpx;
  padding-bottom: 120rpx; // 为底部导航留空间
}

.stats-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  
  .stats-item {
    flex: 1;
    text-align: center;
    
    .stats-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #4A90E2;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .stats-divider {
    width: 2rpx;
    height: 60rpx;
    background: #f0f0f0;
  }
}

.current-exams-section {
  margin-bottom: 40rpx;
  
  .section-header {
    margin-bottom: 30rpx;
    
    .section-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .section-desc {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .exam-list {
    .exam-card {
      position: relative;
      background: #fff;
      border-radius: 24rpx;
      padding: 40rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      
      .exam-type-tag {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
        font-size: 20rpx;
        color: #fff;
        
        &.online {
          background: #4A90E2;
        }
        
        &.offline {
          background: #FF9500;
        }
      }
      
      .exam-info {
        margin-bottom: 30rpx;
        padding-right: 100rpx; // 为标签留空间
        
        .exam-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 12rpx;
          line-height: 1.4;
        }
        
        .exam-desc {
          display: block;
          font-size: 26rpx;
          color: #666;
          margin-bottom: 20rpx;
          line-height: 1.4;
        }
        
        .exam-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 24rpx;
          
          .meta-item {
            display: flex;
            align-items: center;
            
            text {
              margin-left: 8rpx;
              font-size: 24rpx;
              color: #666;
            }
          }
        }
      }
      
      .exam-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .action-btn {
          width: 160rpx;
          height: 64rpx;
          border-radius: 32rpx;
        }
      }
    }
  }
}

.history-section {
  .history-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .history-info {
      display: flex;
      align-items: center;
      flex: 1;
      
      .history-content {
        margin-left: 30rpx;
        
        .history-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .history-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}
</style>
