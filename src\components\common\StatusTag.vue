<template>
  <view class="status-tag" :class="statusClass">
    <text class="status-text">{{ statusText }}</text>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  USER_STATUS_TEXT, 
  EXAM_STATUS_TEXT, 
  EXAM_RECORD_STATUS_TEXT, 
  CERTIFICATE_STATUS_TEXT 
} from '@/constants'

interface Props {
  type: 'user' | 'exam' | 'exam-record' | 'certificate'
  status: string
  text?: string
}

const props = defineProps<Props>()

const statusText = computed(() => {
  if (props.text) return props.text
  
  switch (props.type) {
    case 'user':
      return USER_STATUS_TEXT[props.status] || props.status
    case 'exam':
      return EXAM_STATUS_TEXT[props.status] || props.status
    case 'exam-record':
      return EXAM_RECORD_STATUS_TEXT[props.status] || props.status
    case 'certificate':
      return CERTIFICATE_STATUS_TEXT[props.status] || props.status
    default:
      return props.status
  }
})

const statusClass = computed(() => {
  const baseClass = 'status-tag'
  
  // 根据不同类型和状态返回不同的样式类
  switch (props.type) {
    case 'user':
      switch (props.status) {
        case 'not_submitted':
          return `${baseClass} status-gray`
        case 'pending':
          return `${baseClass} status-warning`
        case 'approved':
          return `${baseClass} status-success`
        case 'rejected':
          return `${baseClass} status-error`
        default:
          return `${baseClass} status-gray`
      }
    
    case 'exam':
      switch (props.status) {
        case 'not_started':
          return `${baseClass} status-gray`
        case 'in_progress':
          return `${baseClass} status-primary`
        case 'completed':
          return `${baseClass} status-success`
        case 'expired':
          return `${baseClass} status-error`
        default:
          return `${baseClass} status-gray`
      }
    
    case 'exam-record':
      switch (props.status) {
        case 'not_started':
          return `${baseClass} status-gray`
        case 'in_progress':
          return `${baseClass} status-primary`
        case 'submitted':
          return `${baseClass} status-warning`
        case 'passed':
          return `${baseClass} status-success`
        case 'failed':
          return `${baseClass} status-error`
        default:
          return `${baseClass} status-gray`
      }
    
    case 'certificate':
      switch (props.status) {
        case 'pending':
          return `${baseClass} status-warning`
        case 'active':
          return `${baseClass} status-success`
        case 'expired':
          return `${baseClass} status-error`
        case 'revoked':
          return `${baseClass} status-error`
        default:
          return `${baseClass} status-gray`
      }
    
    default:
      return `${baseClass} status-gray`
  }
})
</script>

<style lang="scss" scoped>
.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1;
}

.status-text {
  font-size: inherit;
  font-weight: inherit;
}

.status-primary {
  background-color: rgba(46, 139, 87, 0.1);
  color: #2E8B57;
}

.status-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}

.status-warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: #FAAD14;
}

.status-error {
  background-color: rgba(245, 34, 45, 0.1);
  color: #F5222D;
}

.status-gray {
  background-color: rgba(138, 138, 138, 0.1);
  color: #8A8A8A;
}
</style>
