<template>
  <view class="practice-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      :title="navTitle" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
      @leftClick="handleBack"
    />
    
    <!-- 练习进度 -->
    <view class="practice-progress">
      <view class="progress-info">
        <text class="current-question">第 {{ currentQuestionIndex + 1 }} 题</text>
        <text class="total-questions">共 {{ totalQuestions }} 题</text>
      </view>
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
        ></view>
      </view>
      <view class="time-info">
        <u-icon name="clock" color="#666" size="24" />
        <text class="elapsed-time">{{ formatTime(elapsedTime) }}</text>
      </view>
    </view>
    
    <!-- 题目内容 -->
    <view v-if="currentQuestion" class="question-container">
      <view class="question-header">
        <view class="question-type">
          <StatusTag :type="'question'" :status="currentQuestion.type" />
        </view>
        <view v-if="currentQuestion.difficulty" class="question-difficulty">
          <text :class="getDifficultyClass(currentQuestion.difficulty)">
            {{ getDifficultyText(currentQuestion.difficulty) }}
          </text>
        </view>
      </view>
      
      <view class="question-content">
        <text class="question-title">{{ currentQuestion.title }}</text>
      </view>
      
      <!-- 选择题选项 -->
      <view v-if="isChoiceQuestion" class="question-options">
        <view 
          v-for="(option, index) in currentQuestion.options" 
          :key="index"
          class="option-item"
          :class="getOptionClass(index)"
          @click="selectOption(index)"
        >
          <view class="option-indicator">
            <text class="option-label">{{ getOptionLabel(index) }}</text>
            <u-icon 
              v-if="isOptionSelected(index)"
              :name="currentQuestion.type === 'multiple' ? 'checkbox-mark' : 'checkmark-circle-fill'"
              color="#4A90E2"
              size="32"
            />
          </view>
          <text class="option-text">{{ option }}</text>
        </view>
      </view>
      
      <!-- 判断题选项 -->
      <view v-else-if="currentQuestion.type === 'judge'" class="judge-options">
        <view 
          class="judge-option"
          :class="{ active: currentAnswer === true }"
          @click="selectJudge(true)"
        >
          <u-icon 
            :name="currentAnswer === true ? 'checkmark-circle-fill' : 'checkmark-circle'"
            :color="currentAnswer === true ? '#4CAF50' : '#ccc'"
            size="48"
          />
          <text>正确</text>
        </view>
        <view 
          class="judge-option"
          :class="{ active: currentAnswer === false }"
          @click="selectJudge(false)"
        >
          <u-icon 
            :name="currentAnswer === false ? 'close-circle-fill' : 'close-circle'"
            :color="currentAnswer === false ? '#f56c6c' : '#ccc'"
            size="48"
          />
          <text>错误</text>
        </view>
      </view>
      
      <!-- 问答题输入 -->
      <view v-else-if="currentQuestion.type === 'essay'" class="essay-input">
        <u-textarea 
          v-model="currentAnswer"
          placeholder="请输入您的答案..."
          :maxlength="500"
          :showWordLimit="true"
          height="300"
          autoHeight
        />
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <u-button 
        v-if="currentQuestionIndex > 0"
        class="prev-btn"
        type="info"
        plain
        @click="prevQuestion"
      >
        上一题
      </u-button>
      
      <u-button 
        class="next-btn"
        type="primary"
        :disabled="!hasAnswer"
        @click="nextQuestion"
      >
        {{ isLastQuestion ? '完成练习' : '下一题' }}
      </u-button>
    </view>
    
    <!-- 答题卡 -->
    <view class="answer-sheet-btn" @click="showAnswerSheet = true">
      <u-icon name="grid" color="#4A90E2" size="32" />
      <text>答题卡</text>
    </view>
    
    <!-- 答题卡弹窗 -->
    <u-popup 
      v-model="showAnswerSheet" 
      mode="bottom" 
      height="60%"
      :closeOnClickOverlay="true"
    >
      <view class="answer-sheet-modal">
        <view class="modal-header">
          <text class="modal-title">答题卡</text>
          <u-icon name="close" size="32" @click="showAnswerSheet = false" />
        </view>
        <view class="answer-grid">
          <view 
            v-for="(question, index) in questions" 
            :key="question.id"
            class="answer-item"
            :class="getAnswerItemClass(index)"
            @click="jumpToQuestion(index)"
          >
            <text>{{ index + 1 }}</text>
          </view>
        </view>
        <view class="answer-legend">
          <view class="legend-item">
            <view class="legend-color answered"></view>
            <text>已答</text>
          </view>
          <view class="legend-item">
            <view class="legend-color current"></view>
            <text>当前</text>
          </view>
          <view class="legend-item">
            <view class="legend-color unanswered"></view>
            <text>未答</text>
          </view>
        </view>
      </view>
    </u-popup>
    
    <!-- 退出确认弹窗 -->
    <u-modal 
      v-model="showExitModal"
      title="确认退出"
      content="退出后当前练习进度将丢失，确定要退出吗？"
      showCancelButton
      @confirm="confirmExit"
      @cancel="showExitModal = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStudyStore } from '../../src/stores/study'
import { useAppStore } from '../../src/stores/app'
import { PAGE_PATHS } from '../../src/constants'
import { formatDuration } from '../../src/utils'
import type { Question } from '../../src/types'

// 导入组件
import StatusTag from '../../src/components/common/StatusTag.vue'

// Store
const studyStore = useStudyStore()
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  sessionId: string
  categoryName?: string
}>()

// 响应式数据
const showAnswerSheet = ref(false)
const showExitModal = ref(false)
const elapsedTime = ref(0)
let timer: number | null = null

// 计算属性
const currentSession = computed(() => studyStore.currentSession)
const questions = computed(() => currentSession.value?.questions || [])
const currentQuestionIndex = computed(() => studyStore.currentQuestionIndex)
const currentQuestion = computed(() => studyStore.currentQuestion)
const totalQuestions = computed(() => questions.value.length)
const isLastQuestion = computed(() => currentQuestionIndex.value === totalQuestions.value - 1)
const progressPercentage = computed(() => {
  if (totalQuestions.value === 0) return 0
  return ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100
})

const navTitle = computed(() => {
  return props.categoryName || '题库练习'
})

const currentAnswer = computed({
  get: () => {
    if (!currentQuestion.value || !currentSession.value) return null
    return currentSession.value.answers[currentQuestion.value.id] || null
  },
  set: (value) => {
    if (currentQuestion.value) {
      studyStore.answerQuestion(currentQuestion.value.id, value)
    }
  }
})

const hasAnswer = computed(() => {
  if (!currentQuestion.value) return false
  
  const answer = currentAnswer.value
  if (answer === null || answer === undefined) return false
  
  if (currentQuestion.value.type === 'multiple') {
    return Array.isArray(answer) && answer.length > 0
  }
  
  if (currentQuestion.value.type === 'essay') {
    return typeof answer === 'string' && answer.trim().length > 0
  }
  
  return true
})

const isChoiceQuestion = computed(() => {
  return currentQuestion.value?.type === 'single' || currentQuestion.value?.type === 'multiple'
})

onMounted(() => {
  // 检查会话是否存在
  if (!currentSession.value || currentSession.value.id !== props.sessionId) {
    appStore.showToast('练习会话不存在')
    appStore.navigateBack()
    return
  }
  
  // 开始计时
  startTimer()
})

onUnmounted(() => {
  // 清理计时器
  if (timer) {
    clearInterval(timer)
  }
})

// 开始计时
const startTimer = () => {
  timer = setInterval(() => {
    elapsedTime.value++
  }, 1000)
}

// 格式化时间
const formatTime = (seconds: number) => {
  return formatDuration(seconds)
}

// 获取选项标签
const getOptionLabel = (index: number) => {
  return String.fromCharCode(65 + index) // A, B, C, D...
}

// 获取选项样式类
const getOptionClass = (index: number) => {
  const classes = ['option']
  if (isOptionSelected(index)) {
    classes.push('selected')
  }
  return classes.join(' ')
}

// 检查选项是否被选中
const isOptionSelected = (index: number) => {
  if (!currentQuestion.value) return false
  
  const answer = currentAnswer.value
  if (currentQuestion.value.type === 'multiple') {
    return Array.isArray(answer) && answer.includes(index)
  } else {
    return answer === index
  }
}

// 选择选项
const selectOption = (index: number) => {
  if (!currentQuestion.value) return
  
  if (currentQuestion.value.type === 'multiple') {
    let answer = Array.isArray(currentAnswer.value) ? [...currentAnswer.value] : []
    const selectedIndex = answer.indexOf(index)
    
    if (selectedIndex > -1) {
      answer.splice(selectedIndex, 1)
    } else {
      answer.push(index)
    }
    
    currentAnswer.value = answer
  } else {
    currentAnswer.value = index
  }
}

// 选择判断题答案
const selectJudge = (value: boolean) => {
  currentAnswer.value = value
}

// 获取难度样式类
const getDifficultyClass = (difficulty: string) => {
  const classMap = {
    easy: 'difficulty-easy',
    medium: 'difficulty-medium',
    hard: 'difficulty-hard'
  }
  return classMap[difficulty] || 'difficulty-medium'
}

// 获取难度文本
const getDifficultyText = (difficulty: string) => {
  const textMap = {
    easy: '基础',
    medium: '进阶',
    hard: '高级'
  }
  return textMap[difficulty] || '进阶'
}

// 上一题
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    // 这里需要在store中实现跳转到指定题目的方法
    jumpToQuestion(currentQuestionIndex.value - 1)
  }
}

// 下一题
const nextQuestion = () => {
  if (isLastQuestion.value) {
    // 完成练习
    finishPractice()
  } else {
    jumpToQuestion(currentQuestionIndex.value + 1)
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  // 这里需要在store中实现跳转逻辑
  showAnswerSheet.value = false
}

// 获取答题卡项目样式类
const getAnswerItemClass = (index: number) => {
  const classes = ['answer-item']
  
  if (index === currentQuestionIndex.value) {
    classes.push('current')
  } else if (currentSession.value?.answers[questions.value[index]?.id]) {
    classes.push('answered')
  } else {
    classes.push('unanswered')
  }
  
  return classes.join(' ')
}

// 完成练习
const finishPractice = () => {
  if (!currentSession.value) return
  
  // 停止计时
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  
  // 完成会话
  const completedSession = studyStore.completeSession()
  
  if (completedSession) {
    // 跳转到总结页面
    appStore.redirectTo(PAGE_PATHS.STUDY_SUMMARY, { 
      sessionId: completedSession.id 
    })
  }
}

// 处理返回
const handleBack = () => {
  showExitModal.value = true
}

// 确认退出
const confirmExit = () => {
  // 清理当前会话
  studyStore.clearCurrentSession()
  
  // 停止计时
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  
  appStore.navigateBack()
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.practice-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 120rpx;
}

.practice-progress {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  
  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .current-question {
      font-size: 32rpx;
      font-weight: bold;
      color: #4A90E2;
    }
    
    .total-questions {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .progress-bar {
    height: 8rpx;
    background: #f0f0f0;
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 16rpx;
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
      transition: width 0.3s ease;
    }
  }
  
  .time-info {
    display: flex;
    align-items: center;
    gap: 8rpx;
    
    .elapsed-time {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.question-container {
  background: #fff;
  margin: 24rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    
    .question-difficulty {
      .difficulty-easy {
        color: #4CAF50;
        font-size: 24rpx;
        font-weight: bold;
      }
      
      .difficulty-medium {
        color: #FF9500;
        font-size: 24rpx;
        font-weight: bold;
      }
      
      .difficulty-hard {
        color: #f56c6c;
        font-size: 24rpx;
        font-weight: bold;
      }
    }
  }
  
  .question-content {
    margin-bottom: 40rpx;
    
    .question-title {
      font-size: 32rpx;
      line-height: 1.6;
      color: #333;
      font-weight: 500;
    }
  }
  
  .question-options {
    .option-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      border: 2rpx solid #f0f0f0;
      border-radius: 16rpx;
      transition: all 0.3s ease;
      
      &.selected {
        border-color: #4A90E2;
        background: rgba(74, 144, 226, 0.05);
      }
      
      .option-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        margin-right: 24rpx;
        
        .option-label {
          font-size: 28rpx;
          font-weight: bold;
          color: #4A90E2;
        }
      }
      
      .option-text {
        flex: 1;
        font-size: 28rpx;
        line-height: 1.5;
        color: #333;
      }
    }
  }
  
  .judge-options {
    display: flex;
    gap: 40rpx;
    justify-content: center;
    
    .judge-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;
      padding: 40rpx;
      border: 2rpx solid #f0f0f0;
      border-radius: 20rpx;
      flex: 1;
      transition: all 0.3s ease;
      
      &.active {
        border-color: #4A90E2;
        background: rgba(74, 144, 226, 0.05);
      }
      
      text {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
  
  .essay-input {
    margin-top: 20rpx;
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
  
  .prev-btn {
    flex: 1;
  }
  
  .next-btn {
    flex: 2;
  }
}

.answer-sheet-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 120rpx;
  height: 120rpx;
  background: #fff;
  border-radius: 60rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  
  text {
    font-size: 20rpx;
    color: #4A90E2;
    font-weight: bold;
  }
}

.answer-sheet-modal {
  padding: 40rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .answer-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20rpx;
    flex: 1;
    margin-bottom: 40rpx;
    
    .answer-item {
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12rpx;
      font-size: 28rpx;
      font-weight: bold;
      
      &.answered {
        background: #4CAF50;
        color: #fff;
      }
      
      &.current {
        background: #4A90E2;
        color: #fff;
      }
      
      &.unanswered {
        background: #f0f0f0;
        color: #999;
      }
    }
  }
  
  .answer-legend {
    display: flex;
    justify-content: center;
    gap: 40rpx;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 12rpx;
      
      .legend-color {
        width: 24rpx;
        height: 24rpx;
        border-radius: 4rpx;
        
        &.answered {
          background: #4CAF50;
        }
        
        &.current {
          background: #4A90E2;
        }
        
        &.unanswered {
          background: #f0f0f0;
        }
      }
      
      text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}
</style>
