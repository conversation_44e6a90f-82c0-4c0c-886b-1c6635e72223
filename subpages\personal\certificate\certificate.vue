<template>
  <view class="certificate-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="我的证书" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{ certificateStats.total }}</text>
          <text class="stat-label">总证书</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ certificateStats.active }}</text>
          <text class="stat-label">有效证书</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ certificateStats.pending }}</text>
          <text class="stat-label">待发放</text>
        </view>
      </view>
    </view>
    
    <!-- 筛选器 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          v-for="filter in filterOptions" 
          :key="filter.key"
          class="filter-tab"
          :class="{ active: currentFilter === filter.key }"
          @click="currentFilter = filter.key"
        >
          <text>{{ filter.label }}</text>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" text="加载证书信息..." />
    
    <!-- 空状态 -->
    <EmptyState 
      v-else-if="filteredCertificates.length === 0"
      type="no-data"
      title="暂无证书"
      description="完成考试后可获得相应证书"
      :showButton="false"
    />
    
    <!-- 证书列表 -->
    <view v-else class="certificate-list">
      <view 
        v-for="certificate in filteredCertificates" 
        :key="certificate.id"
        class="certificate-item"
        @click="viewCertificate(certificate)"
      >
        <view class="certificate-header">
          <view class="certificate-icon">
            <u-icon name="medal" color="#FFD700" size="60" />
          </view>
          <view class="certificate-info">
            <text class="certificate-name">{{ certificate.name }}</text>
            <text class="certificate-type">{{ certificate.type }}</text>
          </view>
          <view class="certificate-status">
            <StatusTag :type="'certificate'" :status="certificate.status" />
          </view>
        </view>
        
        <view class="certificate-content">
          <view class="certificate-details">
            <view class="detail-item">
              <u-icon name="calendar" color="#666" size="24" />
              <text class="detail-label">获得时间：</text>
              <text class="detail-value">{{ formatDate(certificate.issuedAt, 'YYYY-MM-DD') }}</text>
            </view>
            <view v-if="certificate.expiresAt" class="detail-item">
              <u-icon name="clock" color="#666" size="24" />
              <text class="detail-label">有效期至：</text>
              <text class="detail-value" :class="getExpiryClass(certificate.expiresAt)">
                {{ formatDate(certificate.expiresAt, 'YYYY-MM-DD') }}
              </text>
            </view>
            <view class="detail-item">
              <u-icon name="checkmark-circle" color="#666" size="24" />
              <text class="detail-label">考试成绩：</text>
              <text class="detail-value score">{{ certificate.examScore }}分</text>
            </view>
          </view>
          
          <view class="certificate-actions">
            <u-button 
              class="view-btn"
              type="info"
              size="small"
              plain
              @click.stop="viewCertificate(certificate)"
            >
              查看详情
            </u-button>
            
            <u-button 
              v-if="certificate.status === 'active'"
              class="download-btn"
              type="primary"
              size="small"
              @click.stop="downloadCertificate(certificate)"
            >
              下载证书
            </u-button>
            
            <u-button 
              v-if="certificate.status === 'active'"
              class="share-btn"
              type="success"
              size="small"
              @click.stop="shareCertificate(certificate)"
            >
              分享证书
            </u-button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 证书详情弹窗 -->
    <u-popup 
      v-model="showCertificateDetail" 
      mode="center" 
      width="90%"
      height="80%"
      :closeOnClickOverlay="true"
    >
      <view v-if="selectedCertificate" class="certificate-detail-modal">
        <view class="modal-header">
          <text class="modal-title">证书详情</text>
          <u-icon name="close" size="32" @click="showCertificateDetail = false" />
        </view>
        
        <scroll-view class="modal-content" scroll-y>
          <!-- 证书预览 -->
          <view class="certificate-preview">
            <view class="certificate-frame">
              <view class="certificate-bg">
                <view class="certificate-title">
                  <text>{{ selectedCertificate.name }}</text>
                </view>
                <view class="certificate-recipient">
                  <text>兹证明</text>
                  <text class="recipient-name">{{ userInfo?.realName }}</text>
                  <text>同志</text>
                </view>
                <view class="certificate-content-text">
                  <text>{{ selectedCertificate.description }}</text>
                </view>
                <view class="certificate-footer">
                  <view class="issue-info">
                    <text>发证机构：{{ selectedCertificate.issuer }}</text>
                    <text>发证日期：{{ formatDate(selectedCertificate.issuedAt, 'YYYY年MM月DD日') }}</text>
                  </view>
                  <view class="certificate-number">
                    <text>证书编号：{{ selectedCertificate.certificateNumber }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 证书信息 -->
          <view class="certificate-info-detail">
            <view class="info-item">
              <text class="info-label">证书类型：</text>
              <text class="info-value">{{ selectedCertificate.type }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">考试名称：</text>
              <text class="info-value">{{ selectedCertificate.examName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">考试成绩：</text>
              <text class="info-value">{{ selectedCertificate.examScore }}分</text>
            </view>
            <view class="info-item">
              <text class="info-label">证书状态：</text>
              <StatusTag :type="'certificate'" :status="selectedCertificate.status" />
            </view>
          </view>
        </scroll-view>
        
        <view class="modal-footer">
          <u-button 
            class="download-modal-btn"
            type="primary"
            @click="downloadCertificate(selectedCertificate)"
          >
            下载证书
          </u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { formatDate } from '@/utils'
import api from '@/api'
import type { Certificate } from '@/types'

// 导入组件
import LoadingSpinner from '@/src/components/common/LoadingSpinner.vue'
import EmptyState from '@/src/components/common/EmptyState.vue'
import StatusTag from '@/src/components/common/StatusTag.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const isLoading = ref(true)
const certificates = ref<Certificate[]>([])
const currentFilter = ref('all')
const showCertificateDetail = ref(false)
const selectedCertificate = ref<Certificate | null>(null)

// 筛选选项
const filterOptions = [
  { key: 'all', label: '全部' },
  { key: 'active', label: '有效' },
  { key: 'pending', label: '待发放' },
  { key: 'expired', label: '已过期' }
]

// 证书统计
const certificateStats = ref({
  total: 0,
  active: 0,
  pending: 0,
  expired: 0
})

// 计算属性
const userInfo = computed(() => userStore.userInfo)

const filteredCertificates = computed(() => {
  let filtered = certificates.value
  
  switch (currentFilter.value) {
    case 'active':
      filtered = filtered.filter(cert => cert.status === 'active')
      break
    case 'pending':
      filtered = filtered.filter(cert => cert.status === 'pending')
      break
    case 'expired':
      filtered = filtered.filter(cert => cert.status === 'expired')
      break
  }
  
  return filtered
})

onMounted(() => {
  loadCertificates()
})

// 加载证书列表
const loadCertificates = async () => {
  isLoading.value = true
  
  try {
    const response = await api.certificate.getCertificates()
    certificates.value = response.data
    
    // 计算统计数据
    certificateStats.value = {
      total: certificates.value.length,
      active: certificates.value.filter(cert => cert.status === 'active').length,
      pending: certificates.value.filter(cert => cert.status === 'pending').length,
      expired: certificates.value.filter(cert => cert.status === 'expired').length
    }
  } catch (error: any) {
    console.error('加载证书列表失败:', error)
    appStore.showToast(error.message || '加载失败')
  } finally {
    isLoading.value = false
  }
}

// 获取过期时间样式类
const getExpiryClass = (expiresAt: string) => {
  const expiryDate = new Date(expiresAt)
  const now = new Date()
  const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (daysUntilExpiry < 0) return 'expired'
  if (daysUntilExpiry <= 30) return 'expiring-soon'
  return 'normal'
}

// 查看证书详情
const viewCertificate = (certificate: Certificate) => {
  selectedCertificate.value = certificate
  showCertificateDetail.value = true
}

// 下载证书
const downloadCertificate = async (certificate: Certificate) => {
  try {
    appStore.showLoading('准备下载...')
    
    // 调用下载接口
    const response = await api.certificate.downloadCertificate(certificate.id)
    
    // 保存文件
    uni.downloadFile({
      url: response.data.downloadUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.saveFile({
            tempFilePath: res.tempFilePath,
            success: () => {
              appStore.showToast('证书下载成功', 'success')
            },
            fail: () => {
              appStore.showToast('保存失败')
            }
          })
        }
      },
      fail: () => {
        appStore.showToast('下载失败')
      },
      complete: () => {
        appStore.hideLoading()
      }
    })
  } catch (error: any) {
    appStore.hideLoading()
    console.error('下载证书失败:', error)
    appStore.showToast(error.message || '下载失败')
  }
}

// 分享证书
const shareCertificate = (certificate: Certificate) => {
  // 这里可以实现分享功能
  appStore.showToast('分享功能开发中')
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.certificate-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.stats-section {
  margin: 24rpx;
  
  .stats-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #2E8B57;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .stat-divider {
      width: 2rpx;
      height: 60rpx;
      background: #f0f0f0;
    }
  }
}

.filter-section {
  margin: 0 24rpx 24rpx;
  
  .filter-tabs {
    background: #fff;
    border-radius: 16rpx;
    padding: 8rpx;
    display: flex;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    
    .filter-tab {
      flex: 1;
      text-align: center;
      padding: 16rpx 8rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;
      
      &.active {
        background: #2E8B57;
        
        text {
          color: #fff;
          font-weight: bold;
        }
      }
      
      text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.certificate-list {
  padding: 0 24rpx;
  
  .certificate-item {
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    
    .certificate-header {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      
      .certificate-icon {
        width: 100rpx;
        height: 100rpx;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
      }
      
      .certificate-info {
        flex: 1;
        
        .certificate-name {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
          line-height: 1.4;
        }
        
        .certificate-type {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
    
    .certificate-content {
      .certificate-details {
        margin-bottom: 24rpx;
        
        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .detail-label {
            margin-left: 8rpx;
            font-size: 24rpx;
            color: #666;
            min-width: 140rpx;
          }
          
          .detail-value {
            font-size: 24rpx;
            color: #333;
            
            &.score {
              color: #2E8B57;
              font-weight: bold;
            }
            
            &.normal {
              color: #333;
            }
            
            &.expiring-soon {
              color: #FF9500;
            }
            
            &.expired {
              color: #f56c6c;
            }
          }
        }
      }
      
      .certificate-actions {
        display: flex;
        gap: 16rpx;
        
        .view-btn,
        .download-btn,
        .share-btn {
          flex: 1;
          height: 64rpx;
          border-radius: 32rpx;
        }
      }
    }
  }
}

.certificate-detail-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx;
    border-bottom: 2rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .modal-content {
    flex: 1;
    padding: 40rpx;
  }
  
  .certificate-preview {
    margin-bottom: 40rpx;
    
    .certificate-frame {
      border: 8rpx solid #FFD700;
      border-radius: 16rpx;
      overflow: hidden;
      
      .certificate-bg {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        padding: 60rpx 40rpx;
        text-align: center;
        
        .certificate-title {
          margin-bottom: 40rpx;
          
          text {
            font-size: 36rpx;
            font-weight: bold;
            color: #2E8B57;
          }
        }
        
        .certificate-recipient {
          margin-bottom: 40rpx;
          
          text {
            font-size: 28rpx;
            color: #333;
            
            &.recipient-name {
              font-size: 32rpx;
              font-weight: bold;
              color: #2E8B57;
              margin: 0 16rpx;
            }
          }
        }
        
        .certificate-content-text {
          margin-bottom: 40rpx;
          
          text {
            font-size: 24rpx;
            color: #666;
            line-height: 1.6;
          }
        }
        
        .certificate-footer {
          .issue-info {
            margin-bottom: 20rpx;
            
            text {
              display: block;
              font-size: 20rpx;
              color: #999;
              margin-bottom: 8rpx;
            }
          }
          
          .certificate-number {
            text {
              font-size: 20rpx;
              color: #999;
            }
          }
        }
      }
    }
  }
  
  .certificate-info-detail {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        width: 140rpx;
        font-size: 26rpx;
        color: #666;
      }
      
      .info-value {
        font-size: 26rpx;
        color: #333;
      }
    }
  }
  
  .modal-footer {
    padding: 40rpx;
    border-top: 2rpx solid #f0f0f0;
    
    .download-modal-btn {
      width: 100%;
      height: 80rpx;
      border-radius: 40rpx;
    }
  }
}
</style>
