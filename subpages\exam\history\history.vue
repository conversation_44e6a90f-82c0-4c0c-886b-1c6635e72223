<template>
  <view class="exam-history-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="考试记录" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{ examStats.total }}</text>
          <text class="stat-label">总考试</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ examStats.passed }}</text>
          <text class="stat-label">已通过</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ examStats.averageScore }}</text>
          <text class="stat-label">平均分</text>
        </view>
      </view>
    </view>
    
    <!-- 筛选器 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          v-for="filter in filterOptions" 
          :key="filter.key"
          class="filter-tab"
          :class="{ active: currentFilter === filter.key }"
          @click="currentFilter = filter.key"
        >
          <text>{{ filter.label }}</text>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" text="加载考试记录..." />
    
    <!-- 空状态 -->
    <EmptyState 
      v-else-if="filteredExamHistory.length === 0"
      type="no-data"
      title="暂无考试记录"
      description="您还没有参加过考试"
      :showButton="false"
    />
    
    <!-- 考试记录列表 -->
    <view v-else class="exam-list">
      <view 
        v-for="exam in filteredExamHistory" 
        :key="exam.id"
        class="exam-item"
        @click="viewExamDetail(exam)"
      >
        <view class="exam-header">
          <view class="exam-info">
            <text class="exam-name">{{ exam.examName }}</text>
            <text class="exam-time">{{ formatDate(exam.completedAt, 'YYYY-MM-DD HH:mm') }}</text>
          </view>
          <view class="exam-type">
            <StatusTag :type="'exam-type'" :status="exam.examType" />
          </view>
        </view>
        
        <view class="exam-content">
          <view class="score-section">
            <view class="score-display" :class="getScoreClass(exam.score)">
              <text class="score-number">{{ exam.score }}</text>
              <text class="score-unit">分</text>
            </view>
            <view class="score-info">
              <text class="pass-status" :class="{ passed: exam.passed }">
                {{ exam.passed ? '通过' : '未通过' }}
              </text>
              <text class="score-detail">{{ exam.correctCount }}/{{ exam.totalCount }}题正确</text>
            </view>
          </view>
          
          <view class="exam-meta">
            <view class="meta-item">
              <u-icon name="clock" color="#666" size="24" />
              <text>用时 {{ formatDuration(exam.duration) }}</text>
            </view>
            <view class="meta-item">
              <u-icon name="checkmark-circle" color="#666" size="24" />
              <text>及格线 {{ exam.passScore }}分</text>
            </view>
          </view>
        </view>
        
        <view class="exam-actions">
          <u-button 
            class="detail-btn"
            type="info"
            size="small"
            plain
            @click.stop="viewExamDetail(exam)"
          >
            查看详情
          </u-button>
          
          <u-button 
            v-if="exam.certificateId"
            class="certificate-btn"
            type="success"
            size="small"
            @click.stop="viewCertificate(exam)"
          >
            查看证书
          </u-button>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view v-if="hasMore && !isLoading" class="load-more" @click="loadMore">
      <text>加载更多</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { formatDate, formatDuration } from '@/utils'
import { PAGE_PATHS } from '@/constants'
import api from '@/api'
import type { ExamRecord } from '@/types'

// 导入组件
import LoadingSpinner from '@/src/components/common/LoadingSpinner.vue'
import EmptyState from '@/src/components/common/EmptyState.vue'
import StatusTag from '@/src/components/common/StatusTag.vue'

// Store
const appStore = useAppStore()

// 响应式数据
const isLoading = ref(true)
const examHistory = ref<ExamRecord[]>([])
const currentFilter = ref('all')
const currentPage = ref(1)
const hasMore = ref(true)

// 筛选选项
const filterOptions = [
  { key: 'all', label: '全部' },
  { key: 'passed', label: '已通过' },
  { key: 'failed', label: '未通过' },
  { key: 'online', label: '线上考试' },
  { key: 'offline', label: '线下考试' }
]

// 考试统计
const examStats = ref({
  total: 0,
  passed: 0,
  averageScore: 0
})

// 计算属性
const filteredExamHistory = computed(() => {
  let filtered = examHistory.value
  
  switch (currentFilter.value) {
    case 'passed':
      filtered = filtered.filter(exam => exam.passed)
      break
    case 'failed':
      filtered = filtered.filter(exam => !exam.passed)
      break
    case 'online':
      filtered = filtered.filter(exam => exam.examType === 'online')
      break
    case 'offline':
      filtered = filtered.filter(exam => exam.examType === 'offline')
      break
  }
  
  return filtered
})

onMounted(() => {
  loadExamHistory()
  loadExamStats()
})

// 加载考试记录
const loadExamHistory = async (page = 1) => {
  if (page === 1) {
    isLoading.value = true
  }
  
  try {
    const response = await api.exam.getExamHistory({
      page,
      limit: 10
    })
    
    if (page === 1) {
      examHistory.value = response.data.list
    } else {
      examHistory.value.push(...response.data.list)
    }
    
    hasMore.value = response.data.hasMore
    currentPage.value = page
  } catch (error: any) {
    console.error('加载考试记录失败:', error)
    appStore.showToast(error.message || '加载失败')
  } finally {
    isLoading.value = false
  }
}

// 加载考试统计
const loadExamStats = async () => {
  try {
    const response = await api.exam.getExamStats()
    examStats.value = response.data
  } catch (error: any) {
    console.error('加载考试统计失败:', error)
  }
}

// 获取分数样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 60) return 'normal'
  return 'poor'
}

// 查看考试详情
const viewExamDetail = (exam: ExamRecord) => {
  if (exam.examType === 'online') {
    // 跳转到线上考试结果页面
    appStore.navigateTo(PAGE_PATHS.STUDY_SUMMARY, { 
      sessionId: exam.id 
    })
  } else {
    // 跳转到线下考试详情页面
    appStore.navigateTo(PAGE_PATHS.EXAM_OFFLINE_DETAIL, { 
      examId: exam.examId 
    })
  }
}

// 查看证书
const viewCertificate = (exam: ExamRecord) => {
  appStore.navigateTo(PAGE_PATHS.PERSONAL_CERTIFICATE, { 
    certificateId: exam.certificateId 
  })
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    loadExamHistory(currentPage.value + 1)
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.exam-history-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.stats-section {
  margin: 24rpx;
  
  .stats-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #4A90E2;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .stat-divider {
      width: 2rpx;
      height: 60rpx;
      background: #f0f0f0;
    }
  }
}

.filter-section {
  margin: 0 24rpx 24rpx;
  
  .filter-tabs {
    background: #fff;
    border-radius: 16rpx;
    padding: 8rpx;
    display: flex;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    
    .filter-tab {
      flex: 1;
      text-align: center;
      padding: 16rpx 8rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;
      
      &.active {
        background: #4A90E2;
        
        text {
          color: #fff;
          font-weight: bold;
        }
      }
      
      text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.exam-list {
  padding: 0 24rpx;
  
  .exam-item {
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    
    .exam-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;
      
      .exam-info {
        flex: 1;
        
        .exam-name {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
          line-height: 1.4;
        }
        
        .exam-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .exam-content {
      margin-bottom: 24rpx;
      
      .score-section {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .score-display {
          display: flex;
          align-items: baseline;
          margin-right: 32rpx;
          
          .score-number {
            font-size: 48rpx;
            font-weight: bold;
          }
          
          .score-unit {
            font-size: 24rpx;
            margin-left: 4rpx;
          }
          
          &.excellent {
            color: #4CAF50;
          }
          
          &.good {
            color: #4A90E2;
          }
          
          &.normal {
            color: #FF9500;
          }
          
          &.poor {
            color: #f56c6c;
          }
        }
        
        .score-info {
          flex: 1;
          
          .pass-status {
            display: block;
            font-size: 28rpx;
            font-weight: bold;
            color: #f56c6c;
            margin-bottom: 8rpx;
            
            &.passed {
              color: #4CAF50;
            }
          }
          
          .score-detail {
            font-size: 24rpx;
            color: #666;
          }
        }
      }
      
      .exam-meta {
        display: flex;
        gap: 32rpx;
        
        .meta-item {
          display: flex;
          align-items: center;
          
          text {
            margin-left: 8rpx;
            font-size: 24rpx;
            color: #666;
          }
        }
      }
    }
    
    .exam-actions {
      display: flex;
      gap: 16rpx;
      
      .detail-btn,
      .certificate-btn {
        flex: 1;
        height: 64rpx;
        border-radius: 32rpx;
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: 40rpx;
  
  text {
    font-size: 28rpx;
    color: #4A90E2;
  }
}
</style>
