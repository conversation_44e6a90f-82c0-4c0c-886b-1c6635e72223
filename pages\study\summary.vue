<template>
  <view class="summary-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="练习总结" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" text="生成总结中..." />
    
    <!-- 错误状态 -->
    <EmptyState 
      v-else-if="error"
      type="no-data"
      :title="error"
      description="无法加载练习总结"
      :showButton="true"
      buttonText="返回"
      @buttonClick="goBack"
    />
    
    <!-- 总结内容 -->
    <view v-else-if="session" class="summary-content">
      <!-- 成绩卡片 -->
      <view class="score-card">
        <view class="score-header">
          <view class="score-icon" :class="getScoreLevel(session.score || 0)">
            <u-icon :name="getScoreIcon(session.score || 0)" color="#fff" size="80" />
          </view>
          <view class="score-info">
            <text class="score-number">{{ session.score || 0 }}</text>
            <text class="score-label">分</text>
          </view>
        </view>
        
        <view class="score-details">
          <view class="detail-item">
            <text class="detail-label">正确率</text>
            <text class="detail-value">{{ getAccuracy() }}%</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">答对题数</text>
            <text class="detail-value">{{ session.correctCount || 0 }}/{{ session.totalCount }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">用时</text>
            <text class="detail-value">{{ getDuration() }}</text>
          </view>
        </view>
        
        <view class="score-evaluation">
          <text class="evaluation-text">{{ getEvaluationText() }}</text>
        </view>
      </view>
      
      <!-- 答题详情 -->
      <view class="answer-details">
        <view class="section-header">
          <text class="section-title">答题详情</text>
          <view class="filter-tabs">
            <view 
              v-for="filter in filterOptions" 
              :key="filter.key"
              class="filter-tab"
              :class="{ active: currentFilter === filter.key }"
              @click="currentFilter = filter.key"
            >
              <text>{{ filter.label }}</text>
            </view>
          </view>
        </view>
        
        <view class="question-list">
          <view 
            v-for="(question, index) in filteredQuestions" 
            :key="question.id"
            class="question-item"
            @click="viewQuestionDetail(question, index)"
          >
            <view class="question-header">
              <view class="question-number">
                <text>{{ getQuestionNumber(question.id) }}</text>
              </view>
              <view class="question-result">
                <u-icon 
                  :name="isQuestionCorrect(question) ? 'checkmark-circle-fill' : 'close-circle-fill'"
                  :color="isQuestionCorrect(question) ? '#4CAF50' : '#f56c6c'"
                  size="32"
                />
              </view>
            </view>
            
            <view class="question-content">
              <text class="question-title">{{ question.title }}</text>
              <view class="question-meta">
                <StatusTag :type="'question'" :status="question.type" />
                <text v-if="question.difficulty" class="difficulty">
                  {{ getDifficultyText(question.difficulty) }}
                </text>
              </view>
            </view>
            
            <view class="answer-preview">
              <view class="user-answer">
                <text class="answer-label">我的答案：</text>
                <text class="answer-value" :class="{ correct: isQuestionCorrect(question), wrong: !isQuestionCorrect(question) }">
                  {{ formatUserAnswer(question) }}
                </text>
              </view>
              <view v-if="!isQuestionCorrect(question)" class="correct-answer">
                <text class="answer-label">正确答案：</text>
                <text class="answer-value correct">{{ formatCorrectAnswer(question) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 学习建议 -->
      <view class="study-suggestions">
        <view class="section-header">
          <u-icon name="lightbulb" color="#FF9500" size="32" />
          <text class="section-title">学习建议</text>
        </view>
        
        <view class="suggestion-list">
          <view v-for="suggestion in getSuggestions()" :key="suggestion.type" class="suggestion-item">
            <view class="suggestion-icon">
              <u-icon :name="suggestion.icon" :color="suggestion.color" size="40" />
            </view>
            <view class="suggestion-content">
              <text class="suggestion-title">{{ suggestion.title }}</text>
              <text class="suggestion-desc">{{ suggestion.description }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作 -->
    <view class="bottom-actions">
      <u-button 
        class="action-btn secondary"
        type="info"
        plain
        @click="reviewWrongQuestions"
      >
        错题回顾
      </u-button>
      
      <u-button 
        class="action-btn primary"
        type="primary"
        @click="continueStudy"
      >
        继续练习
      </u-button>
    </view>
    
    <!-- 题目详情弹窗 -->
    <u-popup 
      v-model="showQuestionDetail" 
      mode="bottom" 
      height="80%"
      :closeOnClickOverlay="true"
    >
      <view v-if="selectedQuestion" class="question-detail-modal">
        <view class="modal-header">
          <text class="modal-title">题目详情</text>
          <u-icon name="close" size="32" @click="showQuestionDetail = false" />
        </view>
        
        <scroll-view class="modal-content" scroll-y>
          <view class="detail-question">
            <view class="question-info">
              <StatusTag :type="'question'" :status="selectedQuestion.type" />
              <text class="question-number">第{{ selectedQuestionIndex + 1 }}题</text>
            </view>
            <text class="question-title">{{ selectedQuestion.title }}</text>
          </view>
          
          <!-- 选择题选项 -->
          <view v-if="selectedQuestion.options" class="detail-options">
            <view 
              v-for="(option, index) in selectedQuestion.options" 
              :key="index"
              class="detail-option"
              :class="getDetailOptionClass(selectedQuestion, index)"
            >
              <text class="option-label">{{ getOptionLabel(index) }}.</text>
              <text class="option-text">{{ option }}</text>
            </view>
          </view>
          
          <!-- 答案解析 -->
          <view v-if="selectedQuestion.explanation" class="explanation">
            <view class="explanation-header">
              <u-icon name="book" color="#4A90E2" size="32" />
              <text class="explanation-title">答案解析</text>
            </view>
            <text class="explanation-content">{{ selectedQuestion.explanation }}</text>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useStudyStore } from '../../src/stores/study'
import { useAppStore } from '../../src/stores/app'
import { formatDuration } from '@/utils'
import { PAGE_PATHS } from '@/constants'
import type { Question, PracticeSession } from '@/types'

// 导入组件
import LoadingSpinner from '@/src/components/common/LoadingSpinner.vue'
import EmptyState from '@/src/components/common/EmptyState.vue'
import StatusTag from '@/src/components/common/StatusTag.vue'

// Store
const studyStore = useStudyStore()
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  sessionId: string
}>()

// 响应式数据
const isLoading = ref(true)
const error = ref('')
const session = ref<PracticeSession | null>(null)
const currentFilter = ref('all')
const showQuestionDetail = ref(false)
const selectedQuestion = ref<Question | null>(null)
const selectedQuestionIndex = ref(0)

// 过滤选项
const filterOptions = [
  { key: 'all', label: '全部' },
  { key: 'correct', label: '正确' },
  { key: 'wrong', label: '错误' }
]

// 计算属性
const filteredQuestions = computed(() => {
  if (!session.value) return []
  
  const questions = session.value.questions
  
  switch (currentFilter.value) {
    case 'correct':
      return questions.filter(q => isQuestionCorrect(q))
    case 'wrong':
      return questions.filter(q => !isQuestionCorrect(q))
    default:
      return questions
  }
})

onMounted(() => {
  loadSession()
})

// 加载会话数据
const loadSession = () => {
  isLoading.value = true
  error.value = ''
  
  try {
    // 从练习历史中查找会话
    const foundSession = studyStore.practiceHistory.find(s => s.id === props.sessionId)
    
    if (!foundSession) {
      error.value = '练习记录不存在'
      return
    }
    
    session.value = foundSession
  } catch (err: any) {
    console.error('加载练习总结失败:', err)
    error.value = '加载失败'
  } finally {
    isLoading.value = false
  }
}

// 获取正确率
const getAccuracy = () => {
  if (!session.value || session.value.totalCount === 0) return 0
  return Math.round(((session.value.correctCount || 0) / session.value.totalCount) * 100)
}

// 获取用时
const getDuration = () => {
  if (!session.value || !session.value.endTime) return '未知'
  
  const start = new Date(session.value.startTime).getTime()
  const end = new Date(session.value.endTime).getTime()
  const duration = Math.floor((end - start) / 1000)
  
  return formatDuration(duration)
}

// 获取分数等级
const getScoreLevel = (score: number) => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 60) return 'normal'
  return 'poor'
}

// 获取分数图标
const getScoreIcon = (score: number) => {
  if (score >= 90) return 'trophy'
  if (score >= 80) return 'thumbs-up'
  if (score >= 60) return 'checkmark'
  return 'close'
}

// 获取评价文本
const getEvaluationText = () => {
  const score = session.value?.score || 0
  
  if (score >= 90) return '优秀！您的表现非常出色，继续保持！'
  if (score >= 80) return '良好！您掌握得不错，再接再厉！'
  if (score >= 60) return '及格！还有提升空间，建议多加练习。'
  return '需要加强！建议重点复习相关知识点。'
}

// 检查题目是否正确
const isQuestionCorrect = (question: Question) => {
  if (!session.value) return false
  
  const userAnswer = session.value.answers[question.id]
  return studyStore.isAnswerCorrect(question, userAnswer)
}

// 获取题目序号
const getQuestionNumber = (questionId: string) => {
  if (!session.value) return 0
  
  const index = session.value.questions.findIndex(q => q.id === questionId)
  return index + 1
}

// 获取难度文本
const getDifficultyText = (difficulty: string) => {
  const textMap = {
    easy: '基础',
    medium: '进阶', 
    hard: '高级'
  }
  return textMap[difficulty] || '进阶'
}

// 格式化用户答案
const formatUserAnswer = (question: Question) => {
  if (!session.value) return '未答'
  
  const answer = session.value.answers[question.id]
  if (answer === null || answer === undefined) return '未答'
  
  if (question.type === 'judge') {
    return answer ? '正确' : '错误'
  }
  
  if (question.type === 'single') {
    return question.options?.[answer] || '未知选项'
  }
  
  if (question.type === 'multiple') {
    if (!Array.isArray(answer)) return '未答'
    return answer.map(index => question.options?.[index]).join('、') || '未知选项'
  }
  
  if (question.type === 'essay') {
    return answer.toString().substring(0, 50) + (answer.length > 50 ? '...' : '')
  }
  
  return '未知'
}

// 格式化正确答案
const formatCorrectAnswer = (question: Question) => {
  const answer = question.answer
  
  if (question.type === 'judge') {
    return answer ? '正确' : '错误'
  }
  
  if (question.type === 'single') {
    return question.options?.[answer as number] || '未知'
  }
  
  if (question.type === 'multiple') {
    if (!Array.isArray(answer)) return '未知'
    return answer.map(index => question.options?.[index]).join('、') || '未知'
  }
  
  return answer.toString()
}

// 获取选项标签
const getOptionLabel = (index: number) => {
  return String.fromCharCode(65 + index)
}

// 获取详情选项样式类
const getDetailOptionClass = (question: Question, index: number) => {
  const classes = ['detail-option']
  
  const userAnswer = session.value?.answers[question.id]
  const correctAnswer = question.answer
  
  // 用户选择的选项
  if (question.type === 'single' && userAnswer === index) {
    classes.push(userAnswer === correctAnswer ? 'user-correct' : 'user-wrong')
  }
  
  if (question.type === 'multiple' && Array.isArray(userAnswer) && userAnswer.includes(index)) {
    classes.push('user-selected')
  }
  
  // 正确答案
  if (question.type === 'single' && correctAnswer === index) {
    classes.push('correct-answer')
  }
  
  if (question.type === 'multiple' && Array.isArray(correctAnswer) && correctAnswer.includes(index)) {
    classes.push('correct-answer')
  }
  
  return classes.join(' ')
}

// 获取学习建议
const getSuggestions = () => {
  const suggestions = []
  const score = session.value?.score || 0
  const accuracy = getAccuracy()
  
  if (score < 60) {
    suggestions.push({
      type: 'review',
      icon: 'book',
      color: '#f56c6c',
      title: '加强基础学习',
      description: '建议重新学习相关知识点，打好基础'
    })
  }
  
  if (accuracy < 70) {
    suggestions.push({
      type: 'practice',
      icon: 'edit-pen',
      color: '#FF9500',
      title: '增加练习频次',
      description: '多做练习题，提高答题准确率'
    })
  }
  
  if (score >= 80) {
    suggestions.push({
      type: 'advance',
      icon: 'arrow-up',
      color: '#4CAF50',
      title: '挑战更高难度',
      description: '可以尝试更高难度的题目'
    })
  }
  
  return suggestions
}

// 查看题目详情
const viewQuestionDetail = (question: Question, index: number) => {
  selectedQuestion.value = question
  selectedQuestionIndex.value = index
  showQuestionDetail.value = true
}

// 错题回顾
const reviewWrongQuestions = () => {
  currentFilter.value = 'wrong'
  appStore.showToast('已切换到错题模式')
}

// 继续练习
const continueStudy = () => {
  appStore.navigateTo(PAGE_PATHS.STUDY_CATEGORY)
}

// 返回
const goBack = () => {
  appStore.navigateBack()
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.summary-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 120rpx;
}

.summary-content {
  padding: 24rpx;
}

.score-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  color: #fff;
  
  .score-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40rpx;
    
    .score-icon {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 40rpx;
      
      &.excellent {
        background: #FFD700;
      }
      
      &.good {
        background: #4CAF50;
      }
      
      &.normal {
        background: #FF9500;
      }
      
      &.poor {
        background: #f56c6c;
      }
    }
    
    .score-info {
      display: flex;
      align-items: baseline;
      
      .score-number {
        font-size: 80rpx;
        font-weight: bold;
      }
      
      .score-label {
        font-size: 32rpx;
        margin-left: 8rpx;
      }
    }
  }
  
  .score-details {
    display: flex;
    justify-content: space-around;
    margin-bottom: 32rpx;
    
    .detail-item {
      text-align: center;
      
      .detail-label {
        display: block;
        font-size: 24rpx;
        opacity: 0.8;
        margin-bottom: 8rpx;
      }
      
      .detail-value {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
  
  .score-evaluation {
    text-align: center;
    padding: 24rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16rpx;
    
    .evaluation-text {
      font-size: 28rpx;
      line-height: 1.4;
    }
  }
}

.answer-details,
.study-suggestions {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-left: 16rpx;
    }
    
    .filter-tabs {
      display: flex;
      gap: 16rpx;
      
      .filter-tab {
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        background: #f0f0f0;
        
        &.active {
          background: #4A90E2;
          color: #fff;
        }
        
        text {
          font-size: 24rpx;
        }
      }
    }
  }
}

.question-list {
  .question-item {
    border: 2rpx solid #f0f0f0;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    
    .question-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;
      
      .question-number {
        width: 60rpx;
        height: 60rpx;
        background: #4A90E2;
        border-radius: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        text {
          color: #fff;
          font-size: 24rpx;
          font-weight: bold;
        }
      }
    }
    
    .question-content {
      margin-bottom: 24rpx;
      
      .question-title {
        font-size: 28rpx;
        line-height: 1.5;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .question-meta {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .difficulty {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
    
    .answer-preview {
      .user-answer,
      .correct-answer {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;
        
        .answer-label {
          font-size: 24rpx;
          color: #666;
          margin-right: 16rpx;
        }
        
        .answer-value {
          font-size: 26rpx;
          
          &.correct {
            color: #4CAF50;
          }
          
          &.wrong {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.suggestion-list {
  .suggestion-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .suggestion-icon {
      margin-right: 24rpx;
    }
    
    .suggestion-content {
      flex: 1;
      
      .suggestion-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .suggestion-desc {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
  
  .action-btn {
    flex: 1;
    
    &.secondary {
      flex: 1;
    }
    
    &.primary {
      flex: 1.5;
    }
  }
}

.question-detail-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx;
    border-bottom: 2rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .modal-content {
    flex: 1;
    padding: 40rpx;
  }
  
  .detail-question {
    margin-bottom: 40rpx;
    
    .question-info {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 24rpx;
      
      .question-number {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .question-title {
      font-size: 32rpx;
      line-height: 1.5;
      color: #333;
    }
  }
  
  .detail-options {
    margin-bottom: 40rpx;
    
    .detail-option {
      display: flex;
      align-items: flex-start;
      padding: 20rpx;
      margin-bottom: 16rpx;
      border-radius: 12rpx;
      border: 2rpx solid #f0f0f0;
      
      &.user-correct {
        background: rgba(76, 175, 80, 0.1);
        border-color: #4CAF50;
      }
      
      &.user-wrong {
        background: rgba(245, 108, 108, 0.1);
        border-color: #f56c6c;
      }
      
      &.correct-answer {
        background: rgba(76, 175, 80, 0.1);
        border-color: #4CAF50;
      }
      
      .option-label {
        font-size: 28rpx;
        font-weight: bold;
        color: #4A90E2;
        margin-right: 16rpx;
        min-width: 40rpx;
      }
      
      .option-text {
        flex: 1;
        font-size: 28rpx;
        line-height: 1.4;
        color: #333;
      }
    }
  }
  
  .explanation {
    .explanation-header {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      
      .explanation-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-left: 16rpx;
      }
    }
    
    .explanation-content {
      font-size: 28rpx;
      line-height: 1.6;
      color: #666;
      background: #f8f9fa;
      padding: 24rpx;
      border-radius: 12rpx;
    }
  }
}
</style>
