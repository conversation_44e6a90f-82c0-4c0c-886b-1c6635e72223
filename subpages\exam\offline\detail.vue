<template>
  <view class="offline-exam-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="线下考试详情" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" text="加载考试信息..." />
    
    <!-- 错误状态 -->
    <EmptyState 
      v-else-if="error"
      type="no-data"
      :title="error"
      description="无法加载考试详情"
      :showButton="true"
      buttonText="重新加载"
      @buttonClick="loadExamDetail"
    />
    
    <!-- 考试详情内容 -->
    <view v-else-if="examDetail" class="exam-content">
      <!-- 考试基本信息 -->
      <view class="exam-info-card">
        <view class="exam-header">
          <text class="exam-title">{{ examDetail.name }}</text>
          <StatusTag :type="'exam'" :status="examDetail.status" />
        </view>
        
        <view class="exam-description">
          <text>{{ examDetail.description }}</text>
        </view>
        
        <view class="exam-details">
          <view class="detail-row">
            <u-icon name="calendar" color="#4A90E2" size="32" />
            <text class="detail-label">考试时间：</text>
            <text class="detail-value">{{ formatExamTime() }}</text>
          </view>
          <view class="detail-row">
            <u-icon name="clock" color="#4A90E2" size="32" />
            <text class="detail-label">考试时长：</text>
            <text class="detail-value">{{ examDetail.duration }}分钟</text>
          </view>
          <view class="detail-row">
            <u-icon name="location" color="#4A90E2" size="32" />
            <text class="detail-label">考试地点：</text>
            <text class="detail-value">{{ examDetail.venue || '待安排' }}</text>
          </view>
          <view class="detail-row">
            <u-icon name="file-text" color="#4A90E2" size="32" />
            <text class="detail-label">总分：</text>
            <text class="detail-value">{{ examDetail.totalScore }}分</text>
          </view>
          <view class="detail-row">
            <u-icon name="checkmark-circle" color="#4A90E2" size="32" />
            <text class="detail-label">及格分：</text>
            <text class="detail-value">{{ examDetail.passScore }}分</text>
          </view>
        </view>
      </view>
      
      <!-- 报名状态 -->
      <view class="registration-status">
        <view class="status-header">
          <u-icon name="account" color="#4A90E2" size="32" />
          <text class="status-title">报名状态</text>
        </view>
        
        <view v-if="registrationInfo" class="status-content">
          <view class="status-item">
            <text class="status-label">报名状态：</text>
            <StatusTag :type="'registration'" :status="registrationInfo.status" />
          </view>
          <view class="status-item">
            <text class="status-label">报名时间：</text>
            <text class="status-value">{{ formatDate(registrationInfo.registeredAt, 'YYYY-MM-DD HH:mm') }}</text>
          </view>
          <view v-if="registrationInfo.seatNumber" class="status-item">
            <text class="status-label">座位号：</text>
            <text class="status-value">{{ registrationInfo.seatNumber }}</text>
          </view>
        </view>
        
        <view v-else class="no-registration">
          <u-icon name="info-circle" color="#FF9500" size="40" />
          <text class="no-registration-text">您尚未报名此次考试</text>
        </view>
      </view>
      
      <!-- 考试须知 -->
      <view class="exam-notice">
        <view class="notice-header">
          <u-icon name="warning" color="#FF9500" size="32" />
          <text class="notice-title">考试须知</text>
        </view>
        
        <view class="notice-content">
          <view class="notice-item">
            <text class="notice-number">1</text>
            <text class="notice-text">请携带有效身份证件和准考证参加考试</text>
          </view>
          <view class="notice-item">
            <text class="notice-number">2</text>
            <text class="notice-text">考试开始前30分钟开始入场，开考15分钟后禁止入场</text>
          </view>
          <view class="notice-item">
            <text class="notice-number">3</text>
            <text class="notice-text">考试期间禁止携带手机等电子设备</text>
          </view>
          <view class="notice-item">
            <text class="notice-number">4</text>
            <text class="notice-text">考试结束后统一收卷，不得提前离场</text>
          </view>
        </view>
      </view>
      
      <!-- 联系信息 -->
      <view class="contact-info">
        <view class="contact-header">
          <u-icon name="phone" color="#4A90E2" size="32" />
          <text class="contact-title">联系方式</text>
        </view>
        
        <view class="contact-content">
          <view class="contact-item" @click="makePhoneCall">
            <u-icon name="phone" color="#4CAF50" size="28" />
            <text class="contact-text">考务咨询：************</text>
          </view>
          <view class="contact-item">
            <u-icon name="email" color="#4A90E2" size="28" />
            <text class="contact-text">邮箱：<EMAIL></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作按钮 -->
    <view v-if="examDetail" class="bottom-actions">
      <u-button 
        v-if="canRegister"
        class="register-btn"
        type="primary"
        :loading="isRegistering"
        loadingText="报名中..."
        @click="handleRegister"
      >
        立即报名
      </u-button>
      
      <u-button 
        v-else-if="canCancelRegister"
        class="cancel-btn"
        type="error"
        plain
        @click="handleCancelRegister"
      >
        取消报名
      </u-button>
      
      <u-button 
        v-else-if="canViewResult"
        class="result-btn"
        type="success"
        @click="viewExamResult"
      >
        查看成绩
      </u-button>
      
      <view v-else class="status-tip">
        <text>{{ getStatusTip() }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { formatDate } from '@/utils'
import { PAGE_PATHS } from '@/constants'
import api from '@/api'
import type { Exam, ExamRegistration } from '@/types'

// 导入组件
import LoadingSpinner from '@/src/components/common/LoadingSpinner.vue'
import EmptyState from '@/src/components/common/EmptyState.vue'
import StatusTag from '@/src/components/common/StatusTag.vue'

// Store
const appStore = useAppStore()

// 页面参数
const props = defineProps<{
  examId: string
}>()

// 响应式数据
const isLoading = ref(true)
const error = ref('')
const examDetail = ref<Exam | null>(null)
const registrationInfo = ref<ExamRegistration | null>(null)
const isRegistering = ref(false)

// 计算属性
const canRegister = computed(() => {
  return examDetail.value?.status === 'not_started' && !registrationInfo.value
})

const canCancelRegister = computed(() => {
  return registrationInfo.value?.status === 'registered' && 
         examDetail.value?.status === 'not_started'
})

const canViewResult = computed(() => {
  return examDetail.value?.status === 'completed' && 
         registrationInfo.value?.status === 'completed'
})

onMounted(() => {
  loadExamDetail()
})

// 加载考试详情
const loadExamDetail = async () => {
  isLoading.value = true
  error.value = ''
  
  try {
    // 获取考试详情
    const examResponse = await api.exam.getExamDetail(props.examId)
    examDetail.value = examResponse.data
    
    // 获取报名信息
    try {
      const registrationResponse = await api.exam.getRegistrationInfo(props.examId)
      registrationInfo.value = registrationResponse.data
    } catch (regError) {
      // 未报名的情况，不算错误
      registrationInfo.value = null
    }
  } catch (err: any) {
    console.error('加载考试详情失败:', err)
    error.value = err.message || '加载失败'
  } finally {
    isLoading.value = false
  }
}

// 格式化考试时间
const formatExamTime = () => {
  if (!examDetail.value) return ''
  
  const startTime = new Date(examDetail.value.startTime)
  const endTime = new Date(examDetail.value.endTime)
  
  return `${formatDate(startTime, 'YYYY年MM月DD日 HH:mm')} - ${formatDate(endTime, 'HH:mm')}`
}

// 获取状态提示
const getStatusTip = () => {
  if (!examDetail.value) return ''
  
  switch (examDetail.value.status) {
    case 'not_started':
      return '考试尚未开始'
    case 'in_progress':
      return '考试正在进行中'
    case 'completed':
      return '考试已结束'
    case 'expired':
      return '考试已过期'
    default:
      return ''
  }
}

// 报名考试
const handleRegister = async () => {
  isRegistering.value = true
  
  try {
    await api.exam.registerExam(props.examId)
    appStore.showToast('报名成功', 'success')
    
    // 重新加载数据
    await loadExamDetail()
  } catch (error: any) {
    console.error('报名失败:', error)
    appStore.showToast(error.message || '报名失败，请重试')
  } finally {
    isRegistering.value = false
  }
}

// 取消报名
const handleCancelRegister = () => {
  appStore.showModal({
    title: '确认取消',
    content: '确定要取消报名吗？取消后需要重新报名。',
    confirmText: '确认取消',
    cancelText: '保留报名'
  }).then(async (confirmed) => {
    if (confirmed) {
      try {
        await api.exam.cancelRegistration(props.examId)
        appStore.showToast('取消报名成功', 'success')
        
        // 重新加载数据
        await loadExamDetail()
      } catch (error: any) {
        console.error('取消报名失败:', error)
        appStore.showToast(error.message || '取消报名失败，请重试')
      }
    }
  })
}

// 查看考试成绩
const viewExamResult = () => {
  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY, { examId: props.examId })
}

// 拨打电话
const makePhoneCall = () => {
  uni.makePhoneCall({
    phoneNumber: '************'
  })
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.offline-exam-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 120rpx;
}

.exam-content {
  padding: 24rpx;
}

.exam-info-card,
.registration-status,
.exam-notice,
.contact-info {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.exam-info-card {
  .exam-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .exam-title {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      line-height: 1.4;
    }
  }
  
  .exam-description {
    margin-bottom: 32rpx;
    
    text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .exam-details {
    .detail-row {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .detail-label {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #666;
        min-width: 140rpx;
      }
      
      .detail-value {
        font-size: 26rpx;
        font-weight: bold;
        color: #4A90E2;
      }
    }
  }
}

.registration-status {
  .status-header,
  .notice-header,
  .contact-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    
    .status-title,
    .notice-title,
    .contact-title {
      margin-left: 16rpx;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .status-content {
    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .status-label {
        font-size: 26rpx;
        color: #666;
        min-width: 140rpx;
      }
      
      .status-value {
        font-size: 26rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
  
  .no-registration {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    background: #fff7e6;
    border-radius: 16rpx;
    
    .no-registration-text {
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #FF9500;
    }
  }
}

.exam-notice {
  .notice-content {
    .notice-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .notice-number {
        width: 40rpx;
        height: 40rpx;
        background: #4A90E2;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: bold;
        color: #fff;
        margin-right: 20rpx;
        flex-shrink: 0;
      }
      
      .notice-text {
        flex: 1;
        font-size: 26rpx;
        line-height: 1.5;
        color: #333;
      }
    }
  }
}

.contact-info {
  .contact-content {
    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .contact-text {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #333;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .register-btn,
  .cancel-btn,
  .result-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
  
  .status-tip {
    text-align: center;
    padding: 24rpx;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
