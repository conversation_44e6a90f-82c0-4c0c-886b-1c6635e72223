<template>
  <view class="feedback-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="意见反馈" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 反馈表单 -->
    <view class="feedback-form">
      <!-- 反馈类型 -->
      <view class="form-section">
        <view class="section-title">
          <text>反馈类型</text>
          <text class="required">*</text>
        </view>
        <view class="feedback-types">
          <view 
            v-for="type in feedbackTypes" 
            :key="type.key"
            class="type-item"
            :class="{ active: formData.type === type.key }"
            @click="formData.type = type.key"
          >
            <u-icon :name="type.icon" :color="formData.type === type.key ? '#fff' : '#2E8B57'" size="32" />
            <text>{{ type.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 问题描述 -->
      <view class="form-section">
        <view class="section-title">
          <text>问题描述</text>
          <text class="required">*</text>
        </view>
        <view class="textarea-container">
          <u-textarea 
            v-model="formData.content"
            placeholder="请详细描述您遇到的问题或建议，我们会认真处理每一条反馈"
            :maxlength="500"
            :showWordLimit="true"
            height="300"
            autoHeight
          />
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">
          <text>联系方式</text>
          <text class="optional">（选填）</text>
        </view>
        <view class="contact-inputs">
          <u-input 
            v-model="formData.contact"
            placeholder="请输入手机号或邮箱，便于我们联系您"
            :border="false"
            :customStyle="{ backgroundColor: '#f8f9fa', padding: '24rpx' }"
          />
        </view>
      </view>
      
      <!-- 图片上传 -->
      <view class="form-section">
        <view class="section-title">
          <text>相关截图</text>
          <text class="optional">（选填）</text>
        </view>
        <view class="image-upload">
          <view class="uploaded-images">
            <view 
              v-for="(image, index) in formData.images" 
              :key="index"
              class="image-item"
            >
              <image :src="image" class="uploaded-image" mode="aspectFill" />
              <view class="image-delete" @click="removeImage(index)">
                <u-icon name="close" color="#fff" size="24" />
              </view>
            </view>
            
            <view 
              v-if="formData.images.length < maxImages"
              class="upload-btn"
              @click="chooseImage"
            >
              <u-icon name="camera" color="#2E8B57" size="40" />
              <text class="upload-text">添加图片</text>
              <text class="upload-limit">{{ formData.images.length }}/{{ maxImages }}</text>
            </view>
          </view>
          <view class="upload-tips">
            <text>支持上传{{ maxImages }}张图片，每张不超过5MB</text>
          </view>
        </view>
      </view>
      
      <!-- 常见问题 -->
      <view class="form-section">
        <view class="section-title">
          <text>常见问题</text>
        </view>
        <view class="faq-list">
          <view 
            v-for="faq in faqList" 
            :key="faq.id"
            class="faq-item"
            @click="toggleFaq(faq.id)"
          >
            <view class="faq-question">
              <text>{{ faq.question }}</text>
              <u-icon 
                :name="expandedFaq === faq.id ? 'arrow-up' : 'arrow-down'" 
                color="#666" 
                size="24" 
              />
            </view>
            <view v-if="expandedFaq === faq.id" class="faq-answer">
              <text>{{ faq.answer }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部提交按钮 -->
    <view class="bottom-actions">
      <u-button 
        class="submit-btn"
        type="primary"
        :loading="isSubmitting"
        loadingText="提交中..."
        @click="submitFeedback"
      >
        提交反馈
      </u-button>
    </view>
    
    <!-- 提交成功弹窗 -->
    <u-modal 
      v-model="showSuccessModal"
      title="提交成功"
      content="感谢您的反馈！我们会在3个工作日内处理并回复您。"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="handleSuccessConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAppStore } from '@/stores/app'
import api from '@/api'

// Store
const appStore = useAppStore()

// 响应式数据
const formData = ref({
  type: '',
  content: '',
  contact: '',
  images: [] as string[]
})

const isSubmitting = ref(false)
const showSuccessModal = ref(false)
const expandedFaq = ref('')
const maxImages = 3

// 反馈类型
const feedbackTypes = [
  { key: 'bug', label: '功能异常', icon: 'error-circle' },
  { key: 'suggestion', label: '功能建议', icon: 'lightbulb' },
  { key: 'content', label: '内容问题', icon: 'file-text' },
  { key: 'other', label: '其他问题', icon: 'help-circle' }
]

// 常见问题
const faqList = [
  {
    id: 'login',
    question: '无法登录怎么办？',
    answer: '请检查网络连接，确保微信授权正常。如仍无法登录，请联系技术支持。'
  },
  {
    id: 'practice',
    question: '为什么无法练习题目？',
    answer: '练习功能需要完善个人资料并通过审核。未认证用户每日有练习次数限制。'
  },
  {
    id: 'exam',
    question: '考试时遇到技术问题怎么办？',
    answer: '考试过程中如遇技术问题，请立即联系考务人员或拨打技术支持电话。'
  },
  {
    id: 'certificate',
    question: '证书什么时候能下载？',
    answer: '考试通过后，证书会在5个工作日内生成并可下载。'
  }
]

// 计算属性
const canSubmit = computed(() => {
  return formData.value.type && formData.value.content.trim().length >= 10
})

// 选择图片
const chooseImage = () => {
  const remainingCount = maxImages - formData.value.images.length
  
  uni.chooseImage({
    count: remainingCount,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 检查图片大小
      const validImages: string[] = []
      
      res.tempFilePaths.forEach((path) => {
        uni.getFileInfo({
          filePath: path,
          success: (info) => {
            if (info.size <= 5 * 1024 * 1024) { // 5MB限制
              validImages.push(path)
            } else {
              appStore.showToast('图片大小不能超过5MB')
            }
          }
        })
      })
      
      setTimeout(() => {
        formData.value.images.push(...validImages)
      }, 100)
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      appStore.showToast('选择图片失败')
    }
  })
}

// 删除图片
const removeImage = (index: number) => {
  formData.value.images.splice(index, 1)
}

// 切换FAQ展开状态
const toggleFaq = (faqId: string) => {
  expandedFaq.value = expandedFaq.value === faqId ? '' : faqId
}

// 提交反馈
const submitFeedback = async () => {
  if (!canSubmit.value) {
    appStore.showToast('请选择反馈类型并填写问题描述')
    return
  }
  
  if (formData.value.content.trim().length < 10) {
    appStore.showToast('问题描述至少需要10个字符')
    return
  }
  
  isSubmitting.value = true
  
  try {
    // 上传图片
    const imageUrls: string[] = []
    for (const imagePath of formData.value.images) {
      try {
        // 这里应该调用实际的上传接口
        // const uploadResponse = await api.upload.uploadImage(imagePath)
        // imageUrls.push(uploadResponse.data.url)
        
        // 临时使用本地路径
        imageUrls.push(imagePath)
      } catch (uploadError) {
        console.error('图片上传失败:', uploadError)
      }
    }
    
    // 提交反馈
    await api.feedback.submitFeedback({
      type: formData.value.type,
      content: formData.value.content,
      contact: formData.value.contact,
      images: imageUrls
    })
    
    showSuccessModal.value = true
  } catch (error: any) {
    console.error('提交反馈失败:', error)
    appStore.showToast(error.message || '提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 处理成功确认
const handleSuccessConfirm = () => {
  showSuccessModal.value = false
  
  // 重置表单
  formData.value = {
    type: '',
    content: '',
    contact: '',
    images: []
  }
  
  // 返回上一页
  setTimeout(() => {
    appStore.navigateBack()
  }, 500)
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.feedback-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 120rpx;
}

.feedback-form {
  padding: 24rpx;
}

.form-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    text {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
    
    .required {
      color: #f56c6c;
      margin-left: 8rpx;
    }
    
    .optional {
      color: #999;
      font-size: 24rpx;
      font-weight: normal;
      margin-left: 8rpx;
    }
  }
}

.feedback-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  
  .type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32rpx 20rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 16rpx;
    transition: all 0.3s ease;
    
    &.active {
      background: #2E8B57;
      border-color: #2E8B57;
      
      text {
        color: #fff;
      }
    }
    
    text {
      margin-top: 12rpx;
      font-size: 26rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.textarea-container {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.contact-inputs {
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.image-upload {
  .uploaded-images {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 20rpx;
    
    .image-item {
      position: relative;
      width: 200rpx;
      height: 200rpx;
      border-radius: 12rpx;
      overflow: hidden;
      
      .uploaded-image {
        width: 100%;
        height: 100%;
      }
      
      .image-delete {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 40rpx;
        height: 40rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .upload-btn {
      width: 200rpx;
      height: 200rpx;
      border: 2rpx dashed #2E8B57;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .upload-text {
        margin-top: 12rpx;
        font-size: 24rpx;
        color: #2E8B57;
      }
      
      .upload-limit {
        margin-top: 8rpx;
        font-size: 20rpx;
        color: #999;
      }
    }
  }
  
  .upload-tips {
    text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.faq-list {
  .faq-item {
    border-bottom: 2rpx solid #f8f9fa;
    
    &:last-child {
      border-bottom: none;
    }
    
    .faq-question {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      
      text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
    
    .faq-answer {
      padding-bottom: 24rpx;
      
      text {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .submit-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
