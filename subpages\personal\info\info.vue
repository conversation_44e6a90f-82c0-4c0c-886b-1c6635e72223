<template>
  <view class="personal-info-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="个人信息" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 用户状态提示 -->
    <UserStatusBanner :showAction="false" />
    
    <!-- 个人信息表单 -->
    <view class="info-form">
      <!-- 头像上传 -->
      <view class="form-section">
        <view class="section-title">
          <text>头像照片</text>
        </view>
        <view class="avatar-upload">
          <view class="avatar-container" @click="chooseAvatar">
            <image 
              :src="formData.photo || '/static/images/default-avatar.png'" 
              class="avatar-image"
              mode="aspectFill"
            />
            <view class="avatar-overlay">
              <u-icon name="camera" color="#fff" size="40" />
              <text class="upload-text">更换头像</text>
            </view>
          </view>
          <view class="avatar-tips">
            <text>请上传清晰的个人照片，用于身份验证</text>
          </view>
        </view>
      </view>
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">
          <text>基本信息</text>
        </view>
        <view class="form-items">
          <view class="form-item">
            <text class="item-label">真实姓名</text>
            <u-input 
              v-model="formData.realName"
              placeholder="请输入真实姓名"
              :disabled="!canEdit"
              :border="false"
              :customStyle="inputStyle"
            />
          </view>
          
          <view class="form-item">
            <text class="item-label">身份证号</text>
            <u-input 
              v-model="formData.idCard"
              placeholder="请输入身份证号码"
              :disabled="!canEdit"
              :border="false"
              :customStyle="inputStyle"
            />
          </view>
          
          <view class="form-item">
            <text class="item-label">手机号码</text>
            <u-input 
              v-model="formData.phone"
              placeholder="请输入手机号码"
              type="number"
              :disabled="!canEdit"
              :border="false"
              :customStyle="inputStyle"
            />
          </view>
        </view>
      </view>
      
      <!-- 工作信息 -->
      <view class="form-section">
        <view class="section-title">
          <text>工作信息</text>
        </view>
        <view class="form-items">
          <view class="form-item">
            <text class="item-label">所属机构</text>
            <u-input 
              v-model="formData.organization"
              placeholder="请输入所属机构"
              :disabled="!canEdit"
              :border="false"
              :customStyle="inputStyle"
            />
          </view>
          
          <view class="form-item">
            <text class="item-label">科室部门</text>
            <u-input 
              v-model="formData.department"
              placeholder="请输入科室部门"
              :disabled="!canEdit"
              :border="false"
              :customStyle="inputStyle"
            />
          </view>
          
          <view class="form-item">
            <text class="item-label">职务职称</text>
            <u-input 
              v-model="formData.position"
              placeholder="请输入职务职称"
              :disabled="!canEdit"
              :border="false"
              :customStyle="inputStyle"
            />
          </view>
        </view>
      </view>
      
      <!-- 审核状态 -->
      <view v-if="userInfo?.status !== 'not_submitted'" class="form-section">
        <view class="section-title">
          <text>审核状态</text>
        </view>
        <view class="status-info">
          <view class="status-item">
            <text class="status-label">当前状态：</text>
            <StatusTag :type="'user'" :status="userInfo?.status || ''" />
          </view>
          <view class="status-item">
            <text class="status-label">提交时间：</text>
            <text class="status-value">{{ formatDate(userInfo?.submittedAt, 'YYYY-MM-DD HH:mm') }}</text>
          </view>
          <view v-if="userInfo?.reviewedAt" class="status-item">
            <text class="status-label">审核时间：</text>
            <text class="status-value">{{ formatDate(userInfo?.reviewedAt, 'YYYY-MM-DD HH:mm') }}</text>
          </view>
          <view v-if="userInfo?.rejectReason" class="status-item">
            <text class="status-label">拒绝原因：</text>
            <text class="reject-reason">{{ userInfo.rejectReason }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <u-button 
        v-if="canEdit"
        class="save-btn"
        type="primary"
        :loading="isSaving"
        loadingText="保存中..."
        @click="saveUserInfo"
      >
        保存修改
      </u-button>
      
      <u-button 
        v-else-if="userInfo?.status === 'rejected'"
        class="resubmit-btn"
        type="primary"
        @click="enableEdit"
      >
        重新提交
      </u-button>
      
      <view v-else class="readonly-tip">
        <text>{{ getReadonlyTip() }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { formatDate } from '@/utils'
import { USER_STATUS_TEXT } from '@/constants'
import api from '@/api'

// 导入组件
import UserStatusBanner from '@/src/components/common/UserStatusBanner.vue'
import StatusTag from '@/src/components/common/StatusTag.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const formData = ref({
  realName: '',
  idCard: '',
  phone: '',
  organization: '',
  department: '',
  position: '',
  photo: ''
})

const isSaving = ref(false)
const canEdit = ref(false)

// 计算属性
const userInfo = computed(() => userStore.userInfo)

const inputStyle = computed(() => ({
  backgroundColor: canEdit.value ? '#fff' : '#f8f9fa',
  color: canEdit.value ? '#333' : '#999'
}))

onMounted(() => {
  initFormData()
  checkEditPermission()
})

// 初始化表单数据
const initFormData = () => {
  if (userInfo.value) {
    formData.value = {
      realName: userInfo.value.realName || '',
      idCard: userInfo.value.idCard || '',
      phone: userInfo.value.phone || '',
      organization: userInfo.value.organization || '',
      department: userInfo.value.department || '',
      position: userInfo.value.position || '',
      photo: userInfo.value.photo || ''
    }
  }
}

// 检查编辑权限
const checkEditPermission = () => {
  const status = userInfo.value?.status
  canEdit.value = status === 'not_submitted' || status === 'rejected'
}

// 获取只读提示
const getReadonlyTip = () => {
  const status = userInfo.value?.status
  switch (status) {
    case 'pending':
      return '资料审核中，暂时无法修改'
    case 'approved':
      return '资料已通过审核，如需修改请联系管理员'
    default:
      return '暂时无法修改个人信息'
  }
}

// 启用编辑模式
const enableEdit = () => {
  canEdit.value = true
}

// 选择头像
const chooseAvatar = () => {
  if (!canEdit.value) return
  
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      uploadAvatar(tempFilePath)
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      appStore.showToast('选择图片失败')
    }
  })
}

// 上传头像
const uploadAvatar = async (filePath: string) => {
  appStore.showLoading('上传中...')
  
  try {
    // 这里应该调用实际的上传接口
    // const response = await api.upload.uploadImage(filePath)
    // formData.value.photo = response.data.url
    
    // 临时使用本地路径
    formData.value.photo = filePath
    appStore.hideLoading()
    appStore.showToast('头像上传成功', 'success')
  } catch (error: any) {
    appStore.hideLoading()
    console.error('上传头像失败:', error)
    appStore.showToast(error.message || '上传失败')
  }
}

// 保存用户信息
const saveUserInfo = async () => {
  // 表单验证
  if (!formData.value.realName.trim()) {
    appStore.showToast('请输入真实姓名')
    return
  }
  
  if (!formData.value.idCard.trim()) {
    appStore.showToast('请输入身份证号码')
    return
  }
  
  if (!formData.value.phone.trim()) {
    appStore.showToast('请输入手机号码')
    return
  }
  
  if (!formData.value.organization.trim()) {
    appStore.showToast('请输入所属机构')
    return
  }
  
  // 身份证号格式验证
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (!idCardRegex.test(formData.value.idCard)) {
    appStore.showToast('身份证号码格式不正确')
    return
  }
  
  // 手机号格式验证
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(formData.value.phone)) {
    appStore.showToast('手机号码格式不正确')
    return
  }
  
  isSaving.value = true
  
  try {
    await api.user.updateUserInfo(formData.value)
    
    // 更新本地用户信息
    await userStore.refreshUserInfo()
    
    appStore.showToast('保存成功', 'success')
    canEdit.value = false
    
    // 如果是首次提交，跳转回个人中心
    if (userInfo.value?.status === 'not_submitted') {
      setTimeout(() => {
        appStore.navigateBack()
      }, 1500)
    }
  } catch (error: any) {
    console.error('保存用户信息失败:', error)
    appStore.showToast(error.message || '保存失败，请重试')
  } finally {
    isSaving.value = false
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.personal-info-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
  padding-bottom: 120rpx;
}

.info-form {
  padding: 24rpx;
}

.form-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .section-title {
    margin-bottom: 32rpx;
    
    text {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .avatar-container {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    border-radius: 100rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    
    .avatar-image {
      width: 100%;
      height: 100%;
    }
    
    .avatar-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      .upload-text {
        margin-top: 8rpx;
        font-size: 20rpx;
        color: #fff;
      }
    }
    
    &:active .avatar-overlay {
      opacity: 1;
    }
  }
  
  .avatar-tips {
    text {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}

.form-items {
  .form-item {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #f8f9fa;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-label {
      width: 160rpx;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.status-info {
  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      width: 160rpx;
      font-size: 26rpx;
      color: #666;
    }
    
    .status-value {
      font-size: 26rpx;
      color: #333;
    }
    
    .reject-reason {
      flex: 1;
      font-size: 26rpx;
      color: #f56c6c;
      line-height: 1.5;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .save-btn,
  .resubmit-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
  
  .readonly-tip {
    text-align: center;
    padding: 24rpx;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
