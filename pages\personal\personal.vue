<template>
  <view class="personal-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="header-bg"></view>
      <view class="user-info">
        <view class="avatar-section">
          <image 
            :src="userInfo?.photo || '/static/images/default-avatar.png'" 
            class="user-avatar"
            mode="aspectFill"
            @click="viewAvatar"
          />
          <view v-if="userInfo?.status" class="status-badge" :class="userInfo.status">
            <text>{{ getUserStatusText() }}</text>
          </view>
        </view>
        
        <view class="info-section">
          <text class="user-name">{{ userInfo?.realName || '未设置' }}</text>
          <text class="user-org">{{ userInfo?.organization || '未设置机构' }}</text>
          <view class="user-meta">
            <text class="join-date">注册时间：{{ formatDate(userInfo?.createdAt, 'YYYY-MM-DD') }}</text>
          </view>
        </view>
        
        <view class="action-section">
          <u-icon name="edit-pen" color="#fff" size="32" @click="editProfile" />
        </view>
      </view>
    </view>
    
    <!-- 用户状态提示 -->
    <UserStatusBanner :showAction="true" />
    
    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item" @click="viewStudyStats">
          <text class="stat-number">{{ studyStats.totalSessions }}</text>
          <text class="stat-label">学习次数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" @click="viewExamStats">
          <text class="stat-number">{{ examStats.total }}</text>
          <text class="stat-label">参加考试</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" @click="viewCertificates">
          <text class="stat-number">{{ certificateStats.total }}</text>
          <text class="stat-label">获得证书</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 个人信息管理 -->
      <view class="menu-group">
        <view class="group-title">
          <text>个人信息</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="viewPersonalInfo">
            <view class="menu-icon">
              <u-icon name="account" color="#4A90E2" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">个人资料</text>
              <text class="menu-desc">查看和编辑个人信息</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <view class="menu-item" @click="viewCertificates">
            <view class="menu-icon">
              <u-icon name="medal" color="#FFD700" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">我的证书</text>
              <text class="menu-desc">查看已获得的证书</text>
            </view>
            <view class="menu-badge" v-if="certificateStats.pending > 0">
              <text>{{ certificateStats.pending }}</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
        </view>
      </view>
      
      <!-- 学习记录 -->
      <view class="menu-group">
        <view class="group-title">
          <text>学习记录</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="viewStudyHistory">
            <view class="menu-icon">
              <u-icon name="book" color="#4CAF50" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">学习历史</text>
              <text class="menu-desc">查看练习记录和成绩</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <view class="menu-item" @click="viewExamHistory">
            <view class="menu-icon">
              <u-icon name="file-text" color="#FF9500" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">考试记录</text>
              <text class="menu-desc">查看考试历史和成绩</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
        </view>
      </view>
      
      <!-- 系统设置 -->
      <view class="menu-group">
        <view class="group-title">
          <text>系统设置</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="submitFeedback">
            <view class="menu-icon">
              <u-icon name="chat" color="#9C27B0" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">意见反馈</text>
              <text class="menu-desc">提交问题和建议</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <view class="menu-item" @click="viewAbout">
            <view class="menu-icon">
              <u-icon name="info-circle" color="#607D8B" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">关于我们</text>
              <text class="menu-desc">版本信息和联系方式</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
          
          <view class="menu-item" @click="logout">
            <view class="menu-icon">
              <u-icon name="logout" color="#f56c6c" size="40" />
            </view>
            <view class="menu-content">
              <text class="menu-title">退出登录</text>
              <text class="menu-desc">安全退出当前账号</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="32" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onShow } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { useStudyStore } from '@/stores/study'
import { formatDate } from '@/utils'
import { PAGE_PATHS, USER_STATUS_TEXT } from '@/constants'

// 导入组件
import UserStatusBanner from '@/src/components/common/UserStatusBanner.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()
const studyStore = useStudyStore()

// 系统信息
const statusBarHeight = ref(0)

// 统计数据
const studyStats = ref({
  totalSessions: 0,
  totalQuestions: 0,
  accuracy: 0
})

const examStats = ref({
  total: 0,
  passed: 0,
  pending: 0
})

const certificateStats = ref({
  total: 0,
  pending: 0,
  active: 0
})

// 计算属性
const userInfo = computed(() => userStore.userInfo)

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 加载统计数据
  loadStats()
})

onShow(() => {
  // 页面显示时刷新数据
  loadStats()
})

// 加载统计数据
const loadStats = () => {
  // 学习统计
  studyStats.value = studyStore.getSessionStats()
  
  // 考试统计（这里可以从API获取）
  examStats.value = {
    total: 12,
    passed: 10,
    pending: 2
  }
  
  // 证书统计（这里可以从API获取）
  certificateStats.value = {
    total: 8,
    pending: 1,
    active: 7
  }
}

// 获取用户状态文本
const getUserStatusText = () => {
  if (!userInfo.value?.status) return ''
  return USER_STATUS_TEXT[userInfo.value.status] || userInfo.value.status
}

// 查看头像
const viewAvatar = () => {
  if (userInfo.value?.photo) {
    uni.previewImage({
      urls: [userInfo.value.photo],
      current: userInfo.value.photo
    })
  }
}

// 编辑个人资料
const editProfile = () => {
  if (userInfo.value?.status === 'not_submitted') {
    appStore.redirectTo(PAGE_PATHS.PROFILE)
  } else if (userInfo.value?.status === 'rejected') {
    appStore.redirectTo(PAGE_PATHS.PROFILE)
  } else {
    appStore.navigateTo(PAGE_PATHS.PERSONAL_INFO)
  }
}

// 查看个人信息
const viewPersonalInfo = () => {
  appStore.navigateTo(PAGE_PATHS.PERSONAL_INFO)
}

// 查看证书
const viewCertificates = () => {
  appStore.navigateTo(PAGE_PATHS.PERSONAL_CERTIFICATE)
}

// 查看学习统计
const viewStudyStats = () => {
  appStore.showToast('学习统计功能开发中')
}

// 查看考试统计
const viewExamStats = () => {
  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY)
}

// 查看学习历史
const viewStudyHistory = () => {
  appStore.showToast('学习历史功能开发中')
}

// 查看考试历史
const viewExamHistory = () => {
  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY)
}

// 提交反馈
const submitFeedback = () => {
  appStore.navigateTo(PAGE_PATHS.PERSONAL_FEEDBACK)
}

// 关于我们
const viewAbout = () => {
  appStore.navigateTo(PAGE_PATHS.PERSONAL_ABOUT)
}

// 退出登录
const logout = () => {
  appStore.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    confirmText: '确认退出',
    cancelText: '取消'
  }).then((confirmed) => {
    if (confirmed) {
      userStore.logout()
      appStore.reLaunch(PAGE_PATHS.LOGIN)
    }
  })
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.personal-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.status-bar {
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
}

.user-header {
  position: relative;
  padding-bottom: 40rpx;
  
  .header-bg {
    height: 200rpx;
    background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  }
  
  .user-info {
    position: absolute;
    bottom: 0;
    left: 30rpx;
    right: 30rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    
    .avatar-section {
      position: relative;
      margin-right: 32rpx;
      
      .user-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        border: 4rpx solid #2E8B57;
      }
      
      .status-badge {
        position: absolute;
        bottom: -8rpx;
        right: -8rpx;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        font-size: 18rpx;
        color: #fff;
        
        &.not_submitted {
          background: #FF9500;
        }
        
        &.pending {
          background: #4A90E2;
        }
        
        &.approved {
          background: #4CAF50;
        }
        
        &.rejected {
          background: #f56c6c;
        }
      }
    }
    
    .info-section {
      flex: 1;
      
      .user-name {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .user-org {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .user-meta {
        .join-date {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .action-section {
      width: 60rpx;
      height: 60rpx;
      background: #2E8B57;
      border-radius: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.stats-section {
  margin: 40rpx 30rpx;
  
  .stats-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #2E8B57;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .stat-divider {
      width: 2rpx;
      height: 60rpx;
      background: #f0f0f0;
    }
  }
}

.menu-section {
  padding: 0 30rpx 120rpx;
  
  .menu-group {
    margin-bottom: 40rpx;
    
    .group-title {
      margin-bottom: 20rpx;
      
      text {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
    }
    
    .menu-list {
      background: #fff;
      border-radius: 24rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      
      .menu-item {
        display: flex;
        align-items: center;
        padding: 32rpx;
        border-bottom: 2rpx solid #f8f9fa;
        
        &:last-child {
          border-bottom: none;
        }
        
        .menu-icon {
          width: 80rpx;
          height: 80rpx;
          background: rgba(74, 144, 226, 0.1);
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
        }
        
        .menu-content {
          flex: 1;
          
          .menu-title {
            display: block;
            font-size: 30rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
          }
          
          .menu-desc {
            font-size: 24rpx;
            color: #666;
          }
        }
        
        .menu-badge {
          width: 40rpx;
          height: 40rpx;
          background: #f56c6c;
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16rpx;
          
          text {
            font-size: 20rpx;
            color: #fff;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
