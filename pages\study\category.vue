<template>
  <view class="category-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="选择题库" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 练习次数提示 -->
    <view v-if="userStore.isLoggedIn" class="practice-limit-banner">
      <view class="banner-content">
        <u-icon name="info-circle" color="#4A90E2" size="32" />
        <view class="banner-text">
          <text v-if="userStore.isAuthenticated" class="limit-text unlimited">
            认证用户可无限练习
          </text>
          <text v-else-if="studyStore.canPracticeToday" class="limit-text">
            今日还可练习 {{ studyStore.remainingPracticeCount }} 组
          </text>
          <text v-else class="limit-text limited">
            今日练习次数已用完，明日可继续
          </text>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" text="加载题库分类..." />
    
    <!-- 错误状态 -->
    <EmptyState 
      v-else-if="error"
      type="no-network"
      :title="error"
      description="请检查网络连接后重试"
      :showButton="true"
      buttonText="重新加载"
      @buttonClick="loadCategories"
    />
    
    <!-- 分类列表 -->
    <view v-else class="category-list">
      <!-- 空状态 -->
      <EmptyState 
        v-if="categories.length === 0"
        type="no-data"
        title="暂无题库"
        description="题库内容正在建设中"
        :showButton="false"
      />
      
      <!-- 分类卡片 -->
      <view v-else>
        <view 
          v-for="category in categories" 
          :key="category.id"
          class="category-card"
          @click="selectCategory(category)"
        >
          <view class="card-header">
            <view class="category-icon">
              <u-icon :name="getCategoryIcon(category.name)" color="#4A90E2" size="60" />
            </view>
            <view class="category-info">
              <text class="category-name">{{ category.name }}</text>
              <text class="category-desc">{{ category.description || '专业知识练习' }}</text>
            </view>
            <view class="category-meta">
              <text class="question-count">{{ category.questionCount }}题</text>
              <u-icon name="arrow-right" color="#4A90E2" size="32" />
            </view>
          </view>
          
          <view class="card-footer">
            <view class="difficulty-tags">
              <view class="tag easy">
                <text>基础</text>
              </view>
              <view class="tag medium">
                <text>进阶</text>
              </view>
              <view class="tag hard">
                <text>高级</text>
              </view>
            </view>
            <view class="practice-info">
              <text class="practice-text">{{ getPracticeInfo(category) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部说明 -->
    <view class="bottom-tips">
      <view class="tips-content">
        <u-icon name="lightbulb" color="#FF9500" size="32" />
        <view class="tips-text">
          <text class="tips-title">练习说明</text>
          <text class="tips-desc">每组练习包含10道题目，完成后可查看答案解析。建议先从基础题目开始练习。</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '../../src/stores/user'
import { useAppStore } from '../../src/stores/app'
import { useStudyStore } from '../../src/stores/study'
import { permissionManager } from '../../src/utils/permission'
import { PAGE_PATHS, PRACTICE_CONFIG } from '../../src/constants'
import api from '../../src/api'
import type { QuestionCategory } from '../../src/types'

// 导入组件
import LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'
import EmptyState from '../../src/components/common/EmptyState.vue'

// Store
const userStore = useUserStore()
const appStore = useAppStore()
const studyStore = useStudyStore()

// 响应式数据
const isLoading = ref(true)
const error = ref('')

// 计算属性
const categories = computed(() => studyStore.categories)

onMounted(() => {
  loadCategories()
})

// 加载分类数据
const loadCategories = async () => {
  isLoading.value = true
  error.value = ''
  
  try {
    const response = await api.study.getCategories()
    studyStore.setCategories(response.data)
  } catch (err: any) {
    console.error('加载题库分类失败:', err)
    error.value = err.message || '加载失败'
  } finally {
    isLoading.value = false
  }
}

// 获取分类图标
const getCategoryIcon = (categoryName: string) => {
  const iconMap: Record<string, string> = {
    '疾病预防': 'shield',
    '健康教育': 'book',
    '流行病学': 'search',
    '环境卫生': 'leaf',
    '职业卫生': 'briefcase',
    '营养与食品卫生': 'apple',
    '儿童保健': 'baby',
    '妇女保健': 'female',
    '慢性病防控': 'heart',
    '传染病防控': 'virus'
  }
  
  return iconMap[categoryName] || 'file-text'
}

// 获取练习信息
const getPracticeInfo = (category: QuestionCategory) => {
  if (!userStore.isLoggedIn) {
    return '登录后可练习'
  }
  
  if (userStore.isAuthenticated) {
    return '无限练习'
  }
  
  if (!studyStore.canPracticeToday) {
    return '今日已达上限'
  }
  
  return '可免费练习'
}

// 选择分类
const selectCategory = async (category: QuestionCategory) => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    appStore.showModal({
      title: '需要登录',
      content: '请先登录后再进行题库练习',
      confirmText: '立即登录',
      cancelText: '取消'
    }).then((confirmed) => {
      if (confirmed) {
        appStore.redirectTo(PAGE_PATHS.LOGIN)
      }
    })
    return
  }
  
  // 检查练习次数
  if (!userStore.isAuthenticated && !studyStore.canPracticeToday) {
    appStore.showModal({
      title: '今日练习已达上限',
      content: `免费用户每天可练习${PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY}组题目。完善个人资料并通过机构审核后可享受无限练习特权。`,
      confirmText: '完善资料',
      cancelText: '我知道了'
    }).then((confirmed) => {
      if (confirmed) {
        appStore.switchTab(PAGE_PATHS.PERSONAL)
      }
    })
    return
  }
  
  // 检查题目数量
  if (category.questionCount < PRACTICE_CONFIG.QUESTIONS_PER_SESSION) {
    appStore.showToast(`该分类题目不足${PRACTICE_CONFIG.QUESTIONS_PER_SESSION}道，暂时无法练习`)
    return
  }
  
  // 开始练习
  try {
    appStore.showLoading('准备题目...')
    
    // 获取题目
    const response = await api.study.getQuestions(category.id, PRACTICE_CONFIG.QUESTIONS_PER_SESSION)
    
    if (response.data.length < PRACTICE_CONFIG.QUESTIONS_PER_SESSION) {
      appStore.hideLoading()
      appStore.showToast('题目数量不足，请稍后再试')
      return
    }
    
    // 创建练习会话
    const session = studyStore.startPracticeSession(category.id, response.data)
    
    appStore.hideLoading()
    
    // 跳转到练习页面
    appStore.navigateTo(PAGE_PATHS.STUDY_PRACTICE, { 
      sessionId: session.id,
      categoryName: category.name 
    })
  } catch (err: any) {
    appStore.hideLoading()
    console.error('开始练习失败:', err)
    appStore.showToast(err.message || '开始练习失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.category-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.practice-limit-banner {
  margin: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  
  .banner-content {
    display: flex;
    align-items: center;
    gap: 16rpx;
    
    .banner-text {
      flex: 1;
      
      .limit-text {
        font-size: 26rpx;
        
        &.unlimited {
          color: #4CAF50;
          font-weight: bold;
        }
        
        &.limited {
          color: #f56c6c;
        }
      }
    }
  }
}

.category-list {
  padding: 24rpx;
  
  .category-card {
    background: #fff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    .card-header {
      display: flex;
      align-items: center;
      padding: 40rpx;
      
      .category-icon {
        width: 120rpx;
        height: 120rpx;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 32rpx;
      }
      
      .category-info {
        flex: 1;
        
        .category-name {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 12rpx;
        }
        
        .category-desc {
          font-size: 26rpx;
          color: #666;
          line-height: 1.4;
        }
      }
      
      .category-meta {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;
        
        .question-count {
          font-size: 24rpx;
          color: #4A90E2;
          font-weight: bold;
        }
      }
    }
    
    .card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 40rpx;
      background: #f8f9fa;
      border-top: 2rpx solid #f0f0f0;
      
      .difficulty-tags {
        display: flex;
        gap: 12rpx;
        
        .tag {
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
          font-size: 20rpx;
          
          &.easy {
            background: #f6ffed;
            color: #4CAF50;
            border: 1rpx solid #b7eb8f;
          }
          
          &.medium {
            background: #fff7e6;
            color: #FF9500;
            border: 1rpx solid #ffd591;
          }
          
          &.hard {
            background: #fff2f0;
            color: #f56c6c;
            border: 1rpx solid #ffccc7;
          }
        }
      }
      
      .practice-info {
        .practice-text {
          font-size: 24rpx;
          color: #4A90E2;
          font-weight: bold;
        }
      }
    }
  }
}

.bottom-tips {
  margin: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  
  .tips-content {
    display: flex;
    align-items: flex-start;
    gap: 16rpx;
    
    .tips-text {
      flex: 1;
      
      .tips-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .tips-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}
</style>
